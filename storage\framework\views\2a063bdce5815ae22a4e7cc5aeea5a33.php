<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['onlineCount' => 0]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['onlineCount' => 0]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<footer
    class="bg-gradient-to-b from-[#2d2924] to-[#1f1c18] border-t border-[#514b3c] flex-shrink-0 shadow-inner w-full">
    
    <div class="relative h-3 w-full overflow-hidden">
        <div class="absolute inset-0 bg-[#211f1a]"></div>
        
        <div class="absolute left-1/2 transform -translate-x-1/2 top-0 w-32 h-3 overflow-hidden">
            <div
                class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-80">
            </div>
            <div
                class="absolute inset-x-4 top-1 h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
            </div>
            <div
                class="absolute inset-x-8 top-2 h-px bg-gradient-to-r from-transparent via-[#8a775f] to-transparent opacity-40">
            </div>
        </div>
        
        <div class="absolute left-2 top-1 w-2 h-2 rounded-full border border-[#a6925e] opacity-70"></div>
        <div class="absolute right-2 top-1 w-2 h-2 rounded-full border border-[#a6925e] opacity-70"></div>
    </div>

    
    <div
        class="<?php echo e(auth()->check() && auth()->user()->role === 'admin' ? 'grid-cols-4' : 'grid-cols-3'); ?> grid px-1 py-1 max-w-md mx-auto">
        
        <a href="<?php echo e(route('chat.index')); ?>" class="flex flex-col items-center group transition duration-300 relative">
            <div
                class="w-10 h-10 rounded-full bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border border-[#514b3c] flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 group-hover:border-[#a6925e] group-hover:shadow-md relative">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#d3c6a6] group-hover:text-[#e5b769]"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>

                
                <?php if(auth()->check()): ?>
                    <?php
                        // Получаем счетчики непрочитанных сообщений для всех каналов
                        $user = auth()->user();
                        $currentLocation = $user->statistics->current_location ?? 'Неизвестно';

                        // Используем метод из ChatController для получения счетчиков
                        $chatController = app(\App\Http\Controllers\ChatController::class);
                        $unreadCounts = $chatController->getUnreadMessageCounts($user->id, $currentLocation);

                        // Определяем цвета для каждого канала (соответствуют цветам ников в чате)
                        $channelColors = [
                            'общий' => '#e8e8e8',      // светло-серый/почти белый
                            'торговля' => '#cd7f32',   // коричневатый
                            'гильдия' => '#e5b769',    // золотистый
                            'локация' => '#87ceeb',    // голубоватый
                            'приват' => '#dda0dd'      // фиолетовый
                        ];

                        $totalUnread = 0;
                        $displayCounters = [];

                        foreach ($unreadCounts as $channel => $count) {
                            if ($count > 0) {
                                $totalUnread += $count;
                                $displayCounters[] = [
                                    'channel' => $channel,
                                    'count' => $count > 99 ? '99+' : $count,
                                    'color' => $channelColors[$channel] ?? '#d3c6a6'
                                ];
                            }
                        }
                    ?>

                    <?php if($totalUnread > 0): ?>
                        
                        <?php $__currentLoopData = $displayCounters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $counter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span
                                class="absolute text-[11px] font-bold leading-none px-1 py-0.5 rounded-full min-w-[16px] text-center border border-opacity-30"
                                style="color: <?php echo e($counter['color']); ?>;
                                                                                                   border-color: <?php echo e($counter['color']); ?>;
                                                                                                   background: rgba(0,0,0,0.7);
                                                                                                   top: <?php echo e(-2 + $index * 8); ?>px;
                                                                                                   left: <?php echo e(-8 + $index * -2); ?>px;
                                                                                                   text-shadow: 0 0 4px rgba(0,0,0,0.9), 0 1px 2px rgba(0,0,0,0.8);
                                                                                                   box-shadow: 0 0 6px rgba(<?php echo e($counter['color'] === '#e8e8e8' ? '232,232,232' : ($counter['color'] === '#cd7f32' ? '205,127,50' : '229,183,105')); ?>, 0.4);">
                                <?php echo e($counter['count']); ?>

                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <span
                class="text-[10px] text-[#d3c6a6] mt-1 group-hover:text-[#e5b769] transition-colors duration-300">Чат</span>
        </a>

        
        <a href="<?php echo e(route('forum.index')); ?>" class="flex flex-col items-center group transition duration-300">
            <div
                class="w-10 h-10 rounded-full bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border border-[#514b3c] flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 group-hover:border-[#a6925e] group-hover:shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#d3c6a6] group-hover:text-[#e5b769]"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path
                        d="M17 8h2a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-2v4l-4-4H9a1.994 1.994 0 0 1-1.414-.586m0 0L11 14h4a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2v4l.586-.586z">
                    </path>
                </svg>
            </div>
            <span
                class="text-[10px] text-[#d3c6a6] mt-1 group-hover:text-[#e5b769] transition-colors duration-300">Форум</span>
        </a>

        
        <a href="<?php echo e(route('forum.news.index')); ?>" class="flex flex-col items-center group transition duration-300">
            <div
                class="w-10 h-10 rounded-full bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border border-[#514b3c] flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 group-hover:border-[#a6925e] group-hover:shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#d3c6a6] group-hover:text-[#e5b769]"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path
                        d="M19 20H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v1M15 7h4a2 2 0 0 1 2 2v9.5a1.5 1.5 0 0 1-3 0V9h-3v1">
                    </path>
                </svg>
            </div>
            <span
                class="text-[10px] text-[#d3c6a6] mt-1 group-hover:text-[#e5b769] transition-colors duration-300">Новости</span>
        </a>

        <?php if(auth()->check() && auth()->user()->role === 'admin'): ?>
            <a href="<?php echo e(route('admin.dashboard')); ?>" class="flex flex-col items-center group transition duration-300">
                <div
                    class="w-10 h-10 rounded-full bg-gradient-to-b from-[#413a2d] to-[#2e271d] border border-[#7a6c51] flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 group-hover:border-[#e5b769] group-hover:shadow-lg relative animate-pulse">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94 1.543.826 3.31 2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                        </path>
                        <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <span
                    class="text-[10px] text-[#e5b769] mt-1 group-hover:text-[#f0d89e] transition-colors duration-300">Админка</span>
            </a>
        <?php endif; ?>
    </div>

    
    <div class="grid grid-cols-2 gap-2 px-1 max-w-md mx-auto">
        <?php if (isset($component)) { $__componentOriginalc4352478fc5455aae51e772cb176cf46 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc4352478fc5455aae51e772cb176cf46 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.online-status-card','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.online-status-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc4352478fc5455aae51e772cb176cf46)): ?>
<?php $attributes = $__attributesOriginalc4352478fc5455aae51e772cb176cf46; ?>
<?php unset($__attributesOriginalc4352478fc5455aae51e772cb176cf46); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc4352478fc5455aae51e772cb176cf46)): ?>
<?php $component = $__componentOriginalc4352478fc5455aae51e772cb176cf46; ?>
<?php unset($__componentOriginalc4352478fc5455aae51e772cb176cf46); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginal25b71829f7f87cb4f9b96ea4041df3c4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal25b71829f7f87cb4f9b96ea4041df3c4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.time-card','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.time-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal25b71829f7f87cb4f9b96ea4041df3c4)): ?>
<?php $attributes = $__attributesOriginal25b71829f7f87cb4f9b96ea4041df3c4; ?>
<?php unset($__attributesOriginal25b71829f7f87cb4f9b96ea4041df3c4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal25b71829f7f87cb4f9b96ea4041df3c4)): ?>
<?php $component = $__componentOriginal25b71829f7f87cb4f9b96ea4041df3c4; ?>
<?php unset($__componentOriginal25b71829f7f87cb4f9b96ea4041df3c4); ?>
<?php endif; ?>
    </div>

    
    <div class="flex items-center justify-between px-1 py-1 max-w-md mx-auto">
        
        <div class="flex items-center space-x-2">
            
            <a href="https://t.me/De_6a" class="flex items-center group" target="_blank">
                <div
                    class="flex items-center justify-center w-8 h-8 rounded-full bg-[#252117] border border-[#514b3c] group-hover:border-[#a6925e] group-hover:bg-[#2b271e] transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-[#e5b769] group-hover:text-[#f0d89e]"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path
                            d="M9.78 18.65l.28-4.23 7.68-6.92c.34-.31-.07-.46-.52-.19L7.74 13.3 3.64 12c-.88-.25-.89-.86.2-1.3l15.97-6.16c.73-.33 1.43.18 1.15 1.3l-2.72 12.81c-.19.91-.74 1.13-1.5.71L12.6 16.3l-1.99 1.93c-.23.23-.42.42-.83.42z">
                        </path>
                    </svg>
                </div>
            </a>

            
            <a href="<?php echo e(route('tickets.index')); ?>" class="flex items-center group">
                <div
                    class="flex items-center justify-center w-8 h-8 rounded-full bg-[#252117] border border-[#514b3c] group-hover:border-[#a6925e] group-hover:bg-[#2b271e] transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-[#e5b769] group-hover:text-[#f0d89e]"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 4v-4z" />
                    </svg>
                </div>
            </a>

            
            <?php if(auth()->check() && auth()->user()->role === 'admin'): ?>
                <a href="<?php echo e(route('admin.tickets.index')); ?>" class="flex items-center group">
                    <div
                        class="flex items-center justify-center w-8 h-8 rounded-full bg-[#252117] border border-[#514b3c] group-hover:border-[#a6925e] group-hover:bg-[#2b271e] transition-all duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-[#e5b769] group-hover:text-[#f0d89e]"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                </a>
            <?php endif; ?>
        </div>

        
        <div class="flex items-center justify-center space-x-2">
            
            <?php if (isset($component)) { $__componentOriginal0b6eb46f54aa4087940d22fc719413da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0b6eb46f54aa4087940d22fc719413da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.mobtop-counter','data' => ['type' => 'internal']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.mobtop-counter'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'internal']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0b6eb46f54aa4087940d22fc719413da)): ?>
<?php $attributes = $__attributesOriginal0b6eb46f54aa4087940d22fc719413da; ?>
<?php unset($__attributesOriginal0b6eb46f54aa4087940d22fc719413da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0b6eb46f54aa4087940d22fc719413da)): ?>
<?php $component = $__componentOriginal0b6eb46f54aa4087940d22fc719413da; ?>
<?php unset($__componentOriginal0b6eb46f54aa4087940d22fc719413da); ?>
<?php endif; ?>


        </div>

        
        <form action="<?php echo e(route('logout')); ?>" method="POST" class="inline-block">
            <?php echo csrf_field(); ?>
            <button type="submit"
                class="relative overflow-hidden bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#d3c6a6] text-xs px-4 py-1.5 rounded-md border border-[#514b3c] hover:border-[#a6925e] hover:text-[#e5b769] transition-all duration-300 hover:shadow-md focus:outline-none group">
                <span class="relative z-10">Выйти</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
            </button>
        </form>
    </div>

    
    <div class="relative h-1 w-full max-w-xs mx-auto mb-1">
        <div
            class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-40">
        </div>
    </div>

    
    <div class="text-center text-[#9a9483] text-[10px] py-1 relative">
        © 2025
        
        <?php if(auth()->check() && auth()->user()->role === 'admin'): ?>
            <a href="<?php echo e(route('admin.dashboard')); ?>"
                class="inline-block hover:text-[#d3c6a6] transition-colors duration-300">
                <span class="text-[#d3c6a6] font-medium hover:text-[#e5b769]">Echoes of Eternity</span>
            </a>
        <?php else: ?>
            <span class="text-[#d3c6a6] font-medium">Echoes of Eternity</span>
        <?php endif; ?>
    </div>
</footer><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/footer.blade.php ENDPATH**/ ?>