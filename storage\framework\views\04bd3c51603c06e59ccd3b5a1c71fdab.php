<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['userEffects' => collect()]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['userEffects' => collect()]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="active-effects">
    <?php
        $isStunned = $userEffects->contains(function ($effect) {
            return $effect->isActive() && $effect->isStunEffect();
        });
    ?>

    <?php
        // Фильтруем только активные эффекты
        $activeEffects = $userEffects->filter(function ($effect) {
            return $effect->isActive();
        });
    ?>

    <?php if($activeEffects->isEmpty()): ?>
        <p class="text-gray-400">Нет активных эффектов.</p>
    <?php else: ?>
        <div class="flex flex-row flex-wrap pl-0 ml-0 gap-1 items-start">
            <?php $__currentLoopData = $activeEffects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $effect): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($effect->remaining_duration > 0): ?>
                    <div class="flex flex-col items-center justify-center text-center w-4">
                        <?php if($effect->skill): ?>
                            <img src="<?php echo e(asset($effect->skill->icon ?? 'assets/default_effect.png')); ?>"
                                alt="<?php echo e($effect->skill->name ?? 'Неизвестный эффект'); ?>"
                                class="w-4 h-4 <?php echo e($effect->isStunEffect() ? 'animate-pulse' : ($effect->skill->type === 'debuff' ? 'text-red-400' : 'text-green-400')); ?>">
                        <?php elseif($effect->effect_type == 'mine_detection'): ?>
                            
                            <img src="<?php echo e(asset('assets/obelisk_mark.png')); ?>" alt="Замечен в рудниках"
                                class="w-4 h-4 text-red-400 animate-pulse" title="<?php echo e($effect->effect_name ?? 'Замечен в рудниках'); ?>"
                                style="filter: hue-rotate(45deg) brightness(0.8);" >
                        <?php elseif($effect->effect_type == 'stun' || $effect->isStunEffect()): ?>
                            
                            <img src="<?php echo e(asset('assets/skills/mobs/skillHeavyStrike.png')); ?>" alt="Оглушение"
                                class="w-4 h-4 text-yellow-400 animate-pulse"
                                title="<?php echo e($effect->effect_data['message'] ?? 'Оглушение'); ?>">
                        <?php else: ?>
                            <img src="<?php echo e(asset('assets/default_effect.png')); ?>" alt="Неизвестный эффект" class="w-4 h-4 text-gray-400">
                        <?php endif; ?>
                        <span
                            class="text-[10px] w-full <?php echo e($effect->isStunEffect() ? 'text-yellow-400' : ($effect->effect_type == 'mine_detection' ? 'text-red-400' : (($effect->skill && $effect->skill->type === 'debuff') ? 'text-red-400' : 'text-green-400'))); ?>">
                            <?php echo e(round($effect->remaining_duration)); ?>с
                        </span>
                    </div>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>
</div>


<?php
    $__isStunned = $isStunned ?? false;
?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/active-effects.blade.php ENDPATH**/ ?>