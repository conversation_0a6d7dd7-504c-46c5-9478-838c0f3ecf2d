<?php if($paginator->hasPages()): ?>
    
    <div class="mt-6 mb-4 flex justify-center items-center">
        <div class="relative w-full max-w-xs sm:max-w-sm">
            
            <div
                class="relative bg-gradient-to-b from-[#312e25] to-[#1a1814] rounded-lg px-3 py-2 border border-[#a6925e] shadow-md transform-gpu before:absolute before:inset-0 before:bg-gradient-to-t before:from-transparent before:to-[rgba(229,183,105,0.10)] before:rounded-lg before:pointer-events-none before:opacity-80 after:absolute after:inset-0 after:rounded-lg after:shadow-[inset_0_1px_2px_rgba(255,255,255,0.15),0_3px_8px_rgba(0,0,0,0.4)] after:pointer-events-none overflow-x-auto scrollbar-hide w-full">

                
                <div
                    class="absolute top-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60 pointer-events-none">
                </div>
                <div
                    class="absolute bottom-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60 pointer-events-none">
                </div>

                <div class="flex items-center justify-center w-full min-w-[220px]">
                    
                    <?php if($paginator->onFirstPage()): ?>
                        <button disabled
                            class="w-9 h-9 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 mr-1 pointer-events-none">
                            <span class="text-lg font-medium">←</span>
                        </button>
                    <?php else: ?>
                        <a href="<?php echo e($paginator->previousPageUrl()); ?>"
                            class="w-9 h-9 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] mr-1 hover:text-[#e5b769] pointer-events-auto">
                            <span class="text-lg font-medium">←</span>
                        </a>
                    <?php endif; ?>

                    
                    <div class="flex items-center justify-center">
                        <?php
                            // Русский комментарий: Определяем диапазон страниц для вывода (компактный вариант)
                            $current = $paginator->currentPage();
                            $last = $paginator->lastPage();
                            $delta = 1; // Показываем только по одной странице слева и справа от текущей
                            $range = [];
                            $showFirst = false;
                            $showLast = false;
                            for ($i = max(1, $current - $delta); $i <= min($last, $current + $delta); $i++) {
                                $range[] = $i;
                            }
                            if (!in_array(1, $range))
                                $showFirst = true;
                            if (!in_array($last, $range))
                                $showLast = true;
                        ?>
                        <?php if($showFirst): ?>
                            <a href="<?php echo e($paginator->url(1)); ?>"
                                class="relative w-9 h-9 flex items-center justify-center mx-0.5 rounded border border-[#514b3c] bg-[#252117] text-[#a6925e] hover:bg-[#2d2820] hover:border-[#a6925e] hover:text-[#e5b769] transition-colors duration-300 pointer-events-auto">
                                <span class="text-lg">1</span>
                            </a>
                            <?php if($range[0] > 2): ?>
                                <span class="mx-0.5 text-[#a6925e] text-lg select-none">...</span>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php $__currentLoopData = $range; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e($paginator->url($page)); ?>"
                                class="relative w-9 h-9 flex items-center justify-center mx-0.5 rounded <?php echo e($page == $current ? 'border border-[#e5b769] bg-[#2d2820] text-[#e5b769] font-medium shadow-[inset_0_1px_3px_rgba(0,0,0,0.3)] pointer-events-auto' : 'border border-[#514b3c] bg-[#252117] text-[#a6925e] hover:bg-[#2d2820] hover:border-[#a6925e] hover:text-[#e5b769] pointer-events-auto'); ?> 
                                                                                                            transition-colors duration-300">
                                <?php if($page == $current): ?>
                                    
                                    <div
                                        class="absolute top-0 left-0 w-[5px] h-[5px] border-t border-l border-[#e5b769] opacity-80 pointer-events-none">
                                    </div>
                                    <div
                                        class="absolute top-0 right-0 w-[5px] h-[5px] border-t border-r border-[#e5b769] opacity-80 pointer-events-none">
                                    </div>
                                    <div
                                        class="absolute bottom-0 left-0 w-[5px] h-[5px] border-b border-l border-[#e5b769] opacity-80 pointer-events-none">
                                    </div>
                                    <div
                                        class="absolute bottom-0 right-0 w-[5px] h-[5px] border-b border-r border-[#e5b769] opacity-80 pointer-events-none">
                                    </div>
                                    <span class="text-lg" style="text-shadow: 0 0 4px rgba(229, 183, 105, 0.5);"><?php echo e($page); ?></span>
                                <?php else: ?>
                                    <span class="text-lg"><?php echo e($page); ?></span>
                                <?php endif; ?>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($showLast): ?>
                            <?php if($range[count($range) - 1] < $last - 1): ?>
                                <span class="mx-0.5 text-[#a6925e] text-lg select-none">...</span>
                            <?php endif; ?>
                            <a href="<?php echo e($paginator->url($last)); ?>"
                                class="relative w-9 h-9 flex items-center justify-center mx-0.5 rounded border border-[#514b3c] bg-[#252117] text-[#a6925e] hover:bg-[#2d2820] hover:border-[#a6925e] hover:text-[#e5b769] transition-colors duration-300 pointer-events-auto">
                                <span class="text-lg"><?php echo e($last); ?></span>
                            </a>
                        <?php endif; ?>
                    </div>

                    
                    <?php if($paginator->hasMorePages()): ?>
                        <a href="<?php echo e($paginator->nextPageUrl()); ?>"
                            class="w-9 h-9 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] ml-1 hover:text-[#e5b769] pointer-events-auto">
                            <span class="text-lg font-medium">→</span>
                        </a>
                    <?php else: ?>
                        <button disabled
                            class="w-9 h-9 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 ml-1 pointer-events-none">
                            <span class="text-lg font-medium">→</span>
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    
    <div class="text-center mb-4">
        <div class="inline-block px-3 py-1 bg-[#1a1814] rounded border-t border-b border-[#514b3c]">
            <span class="text-[#9a9483] text-xs">
                Страница <span class="text-[#e5b769]"><?php echo e($paginator->currentPage()); ?></span> из <span
                    class="text-[#e5b769]"><?php echo e($paginator->lastPage()); ?></span>
                <?php if(method_exists($paginator, 'total')): ?>
                    <span class="inline-block mx-1 text-[#514b3c]">•</span>
                    Всего: <span class="text-[#e5b769]"><?php echo e($paginator->total()); ?></span>
                <?php endif; ?>
            </span>
        </div>
    </div>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/pagination/simple-tailwind.blade.php ENDPATH**/ ?>