<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['mob' => null, 'maxEffects' => 6]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['mob' => null, 'maxEffects' => 6]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<?php if($mob && $mob->activeEffects): ?>
    <?php
        $activeEffects = $mob->activeEffects()
            ->where(function($query) {
                $query->where('is_permanent', true)
                      ->orWhere('ends_at', '>', now());
            })
            ->with('skill')
            ->take($maxEffects)
            ->get();
    ?>

    <div class="flex flex-wrap justify-center gap-0.5 w-full min-h-[12px] py-0.5">
        <?php $__empty_1 = true; $__currentLoopData = $activeEffects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $effect): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php
                $isDebuff = in_array($effect->effect_type, ['stun', 'poison', 'weakness', 'slow', 'curse']);
                $iconPath = $effect->skill->icon ?? 'assets/effects/default_effect.png';
                $effectName = $effect->skill->name ?? 'Неизвестный эффект';
                $remainingTime = $effect->is_permanent ? '∞' : max(0, $effect->ends_at->diffInSeconds(now()));
            ?>
            
            <div class="relative group">
                
                <div class="w-2.5 h-2.5 rounded-sm overflow-hidden border border-opacity-50 
                    <?php echo e($isDebuff ? 'border-[#6c4539] bg-[#6c4539]/20' : 'border-[#3e5c48] bg-[#3e5c48]/20'); ?>">
                    <img src="<?php echo e(asset($iconPath)); ?>" 
                         alt="<?php echo e($effectName); ?>"
                         class="w-full h-full object-cover 
                         <?php echo e($isDebuff ? 'filter hue-rotate-[320deg] saturate-150' : 'filter hue-rotate-[90deg] saturate-120'); ?>">
                </div>

                
                <?php if(!$effect->is_permanent && $remainingTime > 0): ?>
                    <div class="absolute -bottom-0.5 -right-0.5 w-1 h-1 rounded-full 
                        <?php echo e($remainingTime > 10 ? 'bg-[#3e5c48]' : ($remainingTime > 5 ? 'bg-[#f28b38]' : 'bg-[#6c4539]')); ?>">
                    </div>
                <?php endif; ?>

                
                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 
                    opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 pointer-events-none">
                    <div class="bg-[#2a1b12] border border-[#6c4539] rounded px-2 py-1 text-xs text-[#fceac4] whitespace-nowrap shadow-lg">
                        <div class="font-semibold"><?php echo e($effectName); ?></div>
                        <?php if(!$effect->is_permanent): ?>
                            <div class="text-[#a6925e] text-xs">
                                <?php echo e($remainingTime > 60 ? floor($remainingTime/60).'м' : $remainingTime.'с'); ?>

                            </div>
                        <?php endif; ?>
                        <?php if($effect->skill && $effect->skill->description): ?>
                            <div class="text-[#a6925e] text-xs mt-1 max-w-32">
                                <?php echo e(Str::limit($effect->skill->description, 50)); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            
            <?php for($i = 0; $i < min(3, $maxEffects); $i++): ?>
                <div class="w-2.5 h-2.5 rounded-sm border border-[#3e342c]/30 bg-[#2a1b12]/20"></div>
            <?php endfor; ?>
        <?php endif; ?>

        
        <?php if($activeEffects->count() >= $maxEffects): ?>
            <?php
                $totalEffects = $mob->activeEffects()
                    ->where(function($query) {
                        $query->where('is_permanent', true)
                              ->orWhere('ends_at', '>', now());
                    })
                    ->count();
                $hiddenEffects = $totalEffects - $maxEffects;
            ?>
            
            <?php if($hiddenEffects > 0): ?>
                <div class="w-2.5 h-2.5 rounded-sm bg-[#f28b38] border border-[#6c4539] 
                    flex items-center justify-center text-[6px] font-bold text-[#2a1b12]">
                    +
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php else: ?>
    
    <div class="flex justify-center gap-0.5 w-full min-h-[12px] py-0.5">
        <?php for($i = 0; $i < min(3, $maxEffects); $i++): ?>
            <div class="w-2.5 h-2.5 rounded-sm border border-[#3e342c]/30 bg-[#2a1b12]/20"></div>
        <?php endfor; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mob-active-effects.blade.php ENDPATH**/ ?>