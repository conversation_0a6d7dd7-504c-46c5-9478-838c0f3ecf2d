<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['routePrefix' => 'battle.outposts.elven_haven', 'skills' => null, 'class' => '', 'isStunned' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['routePrefix' => 'battle.outposts.elven_haven', 'skills' => null, 'class' => '', 'isStunned' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    use Illuminate\Support\Str;
    // Если навыки не переданы, используем навыки текущего пользователя
    $userSkills = $skills ?? (auth()->user()->skills ?? collect());

    // Получаем текущего пользователя
    $user = auth()->user();

    // Проверяем, оглушен ли пользователь
    // Если параметр isStunned не передан, проверяем наличие эффекта оглушения
    if (!isset($isStunned)) {
        $isStunned = false;
        if (isset($userEffects)) {
            $isStunned = $userEffects->contains(function ($effect) {
                return $effect->skill_id == 10 && $effect->isActive();
            });
        }
    }
?>

<div
    class="p-1 bg-gradient-to-b from-[#3b3a33] to-[#2c2b25] border border-[#a6925e] rounded-md shadow-inner shadow-black/50 mt-2 <?php echo e($class); ?>">
    


    
    <div class="flex justify-center gap-2">
        <?php for($i = 0; $i < 4; $i++): ?>
            <?php
                $userSkill = $userSkills[$i] ?? null;
                $hasSkill = $userSkill && $userSkill->skill && $userSkill->skill->type !== 'mob_skill';
                $onCooldown = $hasSkill && $userSkill->cooldown_remaining > 0;
                $statusColor = $isStunned ? 'text-yellow-400' : ($onCooldown ? 'text-red-400' : 'text-green-400');
            ?>

            <div class="flex flex-col items-center">
                <?php if($hasSkill): ?>
                    <?php
                        // Формируем правильный маршрут в зависимости от префикса
                        $routeName = $routePrefix . '.use_skill';

                        // Для подземелий добавляем dungeon в параметры маршрута
                        if (Str::startsWith($routePrefix, 'dungeons')) {
                            $dungeon = request()->route('dungeon');
                            $routeUrl = route($routePrefix . '.use_skill', [$dungeon, $userSkill->skill_id]);
                        }
                        // Для пользовательских шахт добавляем slug в параметры маршрута
                        elseif (Str::startsWith($routePrefix, 'battle.mines.custom')) {
                            $slug = request()->route('slug');
                            $routeUrl = route($routePrefix . '.use_skill', [$slug, $userSkill->skill_id]);
                        }
                        // Для пользовательских аванпостов добавляем id в параметры маршрута
                        elseif (Str::startsWith($routePrefix, 'battle.outposts') && request()->route('id')) {
                            $id = request()->route('id');

                            // Если id не найден, пробуем получить его из запроса
                            if ($id === null) {
                                // Логируем предупреждение
                                \Log::warning('ID аванпоста не найден в маршруте, пробуем получить из запроса', [
                                    'routePrefix' => $routePrefix,
                                    'route_parameters' => request()->route()->parameters(),
                                    'request_all' => request()->all()
                                ]);

                                // Пробуем получить id из других источников
                                $id = request()->input('outpost_id') ?? request()->input('id');
                            }

                            // Логируем для отладки
                            \Log::info('Формирование URL для умения в аванпосте', [
                                'routePrefix' => $routePrefix,
                                'id' => $id,
                                'skillId' => $userSkill->skill_id
                            ]);

                            // Проверяем, что id существует
                            if ($id) {
                                $routeUrl = route($routePrefix . '.use_skill', [$id, $userSkill->skill_id]);
                            } else {
                                // Если id не найден, логируем ошибку и используем запасной вариант
                                \Log::error('ID аванпоста не найден для формирования URL умения', [
                                    'routePrefix' => $routePrefix,
                                    'skillId' => $userSkill->skill_id
                                ]);
                                // Используем запасной вариант - перенаправление на текущую страницу
                                $routeUrl = url()->current();
                            }
                        } else {
                            $routeUrl = route($routePrefix . '.use_skill', $userSkill->skill_id);
                        }
                    ?>

                    <form action="<?php echo e($routeUrl); ?>" method="POST"
                        onsubmit="return <?php echo e($isStunned ? 'false' : 'checkCooldown(this)'); ?>"
                        class="flex flex-col items-center w-full">
                        <?php echo csrf_field(); ?>

                        <?php if($user->current_target_type === 'mob'): ?>
                            <input type="hidden" name="target_type" value="mob">
                            <input type="hidden" name="target_id" value="<?php echo e($user->current_target_id); ?>">
                        <?php elseif($user->current_target_type === 'player'): ?>
                            <input type="hidden" name="target_type" value="user">
                            <input type="hidden" name="target_id" value="<?php echo e($user->current_target_id); ?>">
                        <?php endif; ?>

                        
                        <button type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?>

                            class="relative w-12 h-12 overflow-hidden rounded-md bg-gradient-to-b from-[#5a4d36] to-[#453b2a]
                                                                                                                        border-2 border-[#a6925e] <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:brightness-125 hover:shadow-[0_0_8px_#a6925e50] active:brightness-90'); ?> transition-all">
                            
                            <div
                                class="absolute inset-0 flex items-center justify-center p-0.5 bg-[#1a1915] opacity-50 rounded-sm">
                            </div>

                            
                            <div class="absolute inset-0 flex items-center justify-center">
                                <img src="<?php echo e(asset($userSkill->skill->icon ?? 'assets/skillFireball.png')); ?>"
                                    alt="<?php echo e($userSkill->skill->name); ?>"
                                    class="w-full h-full object-contain <?php echo e($onCooldown || $isStunned ? 'opacity-40 grayscale' : ''); ?>">
                            </div>

                            
                            <?php if($onCooldown): ?>
                                <div class="absolute inset-0 flex items-center justify-center z-20 font-bold text-white text-lg">
                                    <?php echo e(number_format($userSkill->cooldown_remaining, 1)); ?>

                                </div>
                            <?php endif; ?>

                            
                            <?php if($isStunned): ?>
                                <div class="absolute inset-0 flex items-center justify-center z-20 bg-black bg-opacity-40">
                                    <span class="text-yellow-300 text-2xl">⚡</span>
                                </div>
                            <?php endif; ?>
                        </button>

                        
                        <span
                            class="mt-1 text-xs text-center w-full font-bold
                                                                                                                        <?php echo e($statusColor); ?>"
                            data-skill-id="<?php echo e($userSkill->id); ?>" data-cooldown="<?php echo e($userSkill->cooldown_remaining); ?>">
                            <?php echo e($isStunned ? 'оглушен' : ($onCooldown ? 'кд' : 'готово')); ?>

                        </span>
                    </form>
                <?php else: ?>
                    
                    <div
                        class="relative w-12 h-12 rounded-md bg-gradient-to-b from-[#47443b] to-[#302e29]
                                                                                                                    border-2 border-[#695d43] flex items-center justify-center">
                        <div class="absolute inset-0 bg-[#1a1915] opacity-70 rounded-sm"></div>
                        <span class="text-gray-500 text-lg font-bold z-10">—</span>
                    </div>
                    <span class="mt-1 text-xs text-center w-full font-bold text-gray-500">(пусто)</span>
                <?php endif; ?>
            </div>
        <?php endfor; ?>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/skills-panel.blade.php ENDPATH**/ ?>