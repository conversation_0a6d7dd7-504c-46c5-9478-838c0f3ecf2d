<?php
    use App\Helpers\AssetHelper;
?>


<?php echo AssetHelper::getDebugMetaTags(); ?>



<?php if(AssetHelper::isProduction() && !AssetHelper::isAdmin()): ?>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/css/glow-effects.css', 'resources/js/app.js']); ?>
<?php else: ?>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/css/glow-effects.css', 'resources/js/app.js']); ?>


<?php endif; ?>


<?php if(AssetHelper::shouldShowDebugInfo()): ?>
    <?php echo AssetHelper::getDebugJavaScript(); ?>



<?php endif; ?>


<script>
    document.addEventListener('DOMContentLoaded', function () {
        const debugClasses = '<?php echo e(AssetHelper::getDebugClasses()); ?>';
        if (debugClasses) {
            document.body.className += ' ' + debugClasses;
        }
    });
</script><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/secure-assets.blade.php ENDPATH**/ ?>