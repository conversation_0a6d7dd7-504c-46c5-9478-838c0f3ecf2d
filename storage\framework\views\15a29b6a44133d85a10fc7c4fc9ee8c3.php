<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', 'Административная панель'); ?> - Echoes of Eternity</title>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link rel="icon" href="<?php echo e(asset('favicon.png')); ?>" type="image/png">
    
    <style>
        /* Префикс для Bootstrap элементов, чтобы не конфликтовали с Tailwind */
        .bs {
            font-family: var(--bs-body-font-family);
            font-size: var(--bs-body-font-size);
            font-weight: var(--bs-body-font-weight);
            line-height: var(--bs-body-line-height);
        }

        /* Фиксы для админ-страниц с Bootstrap */
        .admin-form-page {
            background-color: #211f1a !important;
            color: #d9d3b8 !important;
        }

        .admin-form-page .card {
            background-color: #2a2721 !important;
            border-color: #514b3c !important;
        }

        .admin-form-page .card-header {
            background-color: #3d3a2e !important;
            color: #e5b769 !important;
            border-color: #514b3c !important;
        }

        .admin-form-page .btn-primary {
            background-color: #a6925e !important;
            border-color: #8c784e !important;
        }

        .admin-form-page .btn-secondary {
            background-color: #514b3c !important;
            border-color: #3d3a2e !important;
        }

        .admin-form-page .form-control,
        .admin-form-page .form-select,
        .admin-form-page .input-group-text {
            background-color: #38352c !important;
            border-color: #514b3c !important;
            color: #d9d3b8 !important;
        }

        .admin-form-page .form-control::placeholder {
            color: #9a9483 !important;
        }

        .admin-form-page .text-muted {
            color: #9a9483 !important;
        }

        /* Стили для таблиц в админке */
        .admin-form-page .table {
            color: #d9d3b8 !important;
            border-color: #514b3c !important;
        }

        .admin-form-page .table thead th {
            background-color: #3d3a2e !important;
            color: #e5b769 !important;
            border-color: #514b3c !important;
        }

        .admin-form-page .table tbody td {
            border-color: #514b3c !important;
        }

        .admin-form-page .table-striped tbody tr:nth-of-type(odd) {
            background-color: #2a2721 !important;
        }

        .admin-form-page .table-striped tbody tr:nth-of-type(even) {
            background-color: #363229 !important;
        }

        .admin-form-page .table-hover tbody tr:hover {
            background-color: #403c32 !important;
            color: #e5b769 !important;
        }

        /* Стили для бейджей */
        .admin-form-page .badge-success {
            background-color: #45592a !important;
            color: #b4d89b !important;
        }

        .admin-form-page .badge-danger {
            background-color: #592a2a !important;
            color: #d89b9b !important;
        }

        .admin-form-page .badge-info {
            background-color: #2a4559 !important;
            color: #9bc5d8 !important;
        }

        .admin-form-page .badge-warning {
            background-color: #594a2a !important;
            color: #d8c59b !important;
        }

        /* Стили для кнопок */
        .admin-form-page .btn-info {
            background-color: #2a4559 !important;
            border-color: #1a3549 !important;
            color: #9bc5d8 !important;
        }

        .admin-form-page .btn-warning {
            background-color: #594a2a !important;
            border-color: #493a1a !important;
            color: #d8c59b !important;
        }

        .admin-form-page .btn-danger {
            background-color: #592a2a !important;
            border-color: #491a1a !important;
            color: #d89b9b !important;
        }

        .admin-form-page .btn-success {
            background-color: #45592a !important;
            border-color: #35491a !important;
            color: #b4d89b !important;
        }

        .admin-form-page .btn-light {
            background-color: #514b3c !important;
            border-color: #413b2c !important;
            color: #d9d3b8 !important;
        }

        /* Стили для фона */
        .admin-form-page .bg-light {
            background-color: #363229 !important;
            color: #d9d3b8 !important;
        }

        /* Стили для алертов */
        .admin-form-page .alert-success {
            background-color: #374529 !important;
            border-color: #596c3b !important;
            color: #b4d89b !important;
        }

        .admin-form-page .alert-danger {
            background-color: #452929 !important;
            border-color: #6c3b3b !important;
            color: #d89b9b !important;
        }

        .admin-form-page .alert-info {
            background-color: #293645 !important;
            border-color: #3b4c6c !important;
            color: #9bb4d8 !important;
        }

        /* Стили для кода */
        .admin-form-page code {
            background-color: #38352c !important;
            color: #e5b769 !important;
            border-radius: 3px;
            padding: 2px 4px;
        }

        /* Стили для вложенных таблиц */
        .admin-form-page .table .bg-light {
            background-color: #363229 !important;
        }

        .admin-form-page .table .bg-white {
            background-color: #2a2721 !important;
            color: #d9d3b8 !important;
        }

        .admin-form-page .table .thead-light th {
            background-color: #3d3a2e !important;
            color: #e5b769 !important;
            border-color: #514b3c !important;
        }

        /* Стили для коллапсов */
        .admin-form-page .collapse {
            background-color: #2a2721 !important;
        }

        /* Стили для пагинации */
        .admin-form-page .pagination {
            --bs-pagination-color: #d9d3b8;
            --bs-pagination-bg: #2a2721;
            --bs-pagination-border-color: #514b3c;
            --bs-pagination-hover-color: #e5b769;
            --bs-pagination-hover-bg: #3d3a2e;
            --bs-pagination-hover-border-color: #8c784e;
            --bs-pagination-active-color: #2a2721;
            --bs-pagination-active-bg: #a6925e;
            --bs-pagination-active-border-color: #8c784e;
            --bs-pagination-disabled-color: #9a9483;
            --bs-pagination-disabled-bg: #363229;
            --bs-pagination-disabled-border-color: #514b3c;
        }

        /* Стили для ссылок */
        .admin-form-page a {
            color: #a6925e;
        }

        .admin-form-page a:hover {
            color: #e5b769;
        }

        .admin-form-page .alert-link {
            color: #e5b769 !important;
            text-decoration: underline;
        }

        /* Стили для кнопок-ссылок */
        .admin-form-page .btn-outline-info {
            color: #9bc5d8 !important;
            border-color: #2a4559 !important;
            background-color: transparent !important;
        }

        .admin-form-page .btn-outline-info:hover {
            background-color: #2a4559 !important;
            color: #9bc5d8 !important;
        }

        /* Стили для Select2 */
        .admin-form-page .select2-container--bootstrap4 .select2-selection {
            background-color: #38352c !important;
            border-color: #514b3c !important;
            color: #d9d3b8 !important;
        }

        .admin-form-page .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
            color: #d9d3b8 !important;
        }

        .admin-form-page .select2-container--bootstrap4 .select2-dropdown {
            background-color: #2a2721 !important;
            border-color: #514b3c !important;
        }

        .admin-form-page .select2-container--bootstrap4 .select2-results__option {
            color: #d9d3b8 !important;
        }

        .admin-form-page .select2-container--bootstrap4 .select2-results__option--highlighted[aria-selected] {
            background-color: #514b3c !important;
            color: #e5b769 !important;
        }

        .admin-form-page .select2-container--bootstrap4 .select2-results__option[aria-selected=true] {
            background-color: #a6925e !important;
            color: #2a2721 !important;
        }

        /* Стили для текстовых полей */
        .admin-form-page textarea {
            background-color: #38352c !important;
            border-color: #514b3c !important;
            color: #d9d3b8 !important;
        }

        /* Стили для карточек */
        .admin-form-page .card-outline.card-info {
            border-top: 3px solid #2a4559 !important;
        }

        .admin-form-page .card-info .card-header {
            background-color: #2a4559 !important;
            color: #9bc5d8 !important;
        }

        /* Стили для текстовых цветов */
        .admin-form-page .text-info {
            color: #9bc5d8 !important;
        }

        .admin-form-page .text-danger {
            color: #d89b9b !important;
        }

        .admin-form-page .font-weight-bold {
            color: #e5b769 !important;
        }
    </style>

    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" class="bs">

    
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">

    <?php echo $__env->yieldContent('styles'); ?>
</head>

<body class="bg-[#211f1a] text-[#d9d3b8] font-serif">
    
    <div class="min-h-screen flex flex-col">
        
        <header class="bg-[#2a2721] border-b border-[#514b3c] py-4">
            <div class="container mx-auto px-4">
                
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center">
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-xl font-bold text-[#e5b769]">
                            Echoes of Eternity | Админ-панель
                        </a>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="<?php echo e(route('home')); ?>"
                            class="text-[#d9d3b8] hover:text-[#e5b769] transition duration-300">
                            На главную
                        </a>
                        <span class="text-[#514b3c]">|</span>
                        <form action="<?php echo e(route('logout')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="text-[#d9d3b8] hover:text-[#e5b769] transition duration-300">
                                Выйти
                            </button>
                        </form>
                    </div>
                </div>

                
                <nav class="flex space-x-6">
                    <a href="<?php echo e(route('admin.dashboard')); ?>"
                        class="text-[#d9d3b8] hover:text-[#e5b769] transition duration-300 <?php echo e(request()->routeIs('admin.dashboard') ? 'text-[#e5b769] font-medium' : ''); ?>">
                        📊 Dashboard
                    </a>
                    <a href="<?php echo e(route('admin.tickets.index')); ?>"
                        class="text-[#d9d3b8] hover:text-[#e5b769] transition duration-300 <?php echo e(request()->routeIs('admin.tickets.*') ? 'text-[#e5b769] font-medium' : ''); ?>">
                        🎫 Тикеты
                    </a>
                    <a href="<?php echo e(route('admin.testers.index')); ?>"
                        class="text-[#d9d3b8] hover:text-[#e5b769] transition duration-300 <?php echo e(request()->routeIs('admin.testers.*') ? 'text-[#e5b769] font-medium' : ''); ?>">
                        🧪 Тестировщики
                    </a>
                </nav>
            </div>
        </header>

        
        <?php if(session('success')): ?>
            <div class="container mx-auto px-4 mt-4">
                <div
                    class="bg-[#36513f] text-[#c8ffdb] p-4 rounded-lg border border-[#4a7759] shadow-inner shadow-black/30">
                    <?php echo session('success'); ?>

                </div>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="container mx-auto px-4 mt-4">
                <div
                    class="bg-[#613f36] text-[#ffeac1] p-4 rounded-lg border border-[#88634a] shadow-inner shadow-black/30">
                    <?php echo session('error'); ?>

                </div>
            </div>
        <?php endif; ?>

        <?php if(session('info')): ?>
            <div class="container mx-auto px-4 mt-4">
                <div
                    class="bg-[#36465d] text-[#c8e1ff] p-4 rounded-lg border border-[#4a5e77] shadow-inner shadow-black/30">
                    <?php echo session('info'); ?>

                </div>
            </div>
        <?php endif; ?>

        
        <?php if($errors->any()): ?>
            <div class="container mx-auto px-4 mt-4">
                <div
                    class="bg-[#613f36] text-[#ffeac1] p-4 rounded-lg border border-[#88634a] shadow-inner shadow-black/30">
                    <h4 class="font-bold mb-2">Пожалуйста, исправьте следующие ошибки:</h4>
                    <ul class="list-disc list-inside">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        <?php endif; ?>

        
        <main class="flex-grow py-6">
            <?php echo $__env->yieldContent('content'); ?>
        </main>

        
        <footer class="bg-[#2a2721] text-[#9a9483] py-4 mt-auto border-t border-[#514b3c]">
            <div class="container mx-auto px-4 text-center text-sm">
                © <?php echo e(date('Y')); ?> Echoes of Eternity | Административная Панель
            </div>
        </footer>
    </div>

    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        // Инициализация Select2 для улучшенных селектов
        $(document).ready(function () {
            $('.select2').select2({
                theme: 'bootstrap-5'
            });

            // Инициализация BS5 Custom File Input
            document.querySelectorAll('.custom-file-input').forEach(input => {
                input.addEventListener('change', function () {
                    let fileName = this.files[0]?.name || 'Выберите файл';
                    let label = this.nextElementSibling;
                    label.innerText = fileName;
                });
            });

            // Предварительный просмотр изображения
            document.getElementById('image')?.addEventListener('change', function () {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        document.getElementById('image-preview').src = e.target.result;
                    }
                    reader.readAsDataURL(file);
                }
            });
        });
    </script>
    <?php echo $__env->yieldContent('scripts'); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/layouts/app.blade.php ENDPATH**/ ?>