<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['solWarriors' => 0, 'solMages' => 0, 'solKnights' => 0, 'lunWarriors' => 0, 'lunMages' => 0, 'lunKnights' => 0, 'onlineCount' => 0]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['solWarriors' => 0, 'solMages' => 0, 'solKnights' => 0, 'lunWarriors' => 0, 'lunMages' => 0, 'lunKnights' => 0, 'onlineCount' => 0]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div
    class="bg-[#2f2d2b] border border-[#514b3c] rounded-lg shadow-inner w-full mt-0 p-0 mb-2 flex justify-between items-center">
    
    <div class="flex items-center space-x-1">
        <img src="<?php echo e(asset('assets/race/Racesolarius.png')); ?>" alt="Солариус" class="w-5 h-5">
        <div class="flex items-center space-x-1">
            <img src="<?php echo e(asset('assets/race/raceSolarWarrior.png')); ?>" alt="Воин" class="w-4 h-4">
            <span class="text-xs text-red-400"><?php echo e($solWarriors); ?></span> 
        </div>
        <div class="flex items-center space-x-1">
            <img src="<?php echo e(asset('assets/race/raceSolarMage.png')); ?>" alt="Маг" class="w-4 h-4">
            <span
                class="text-xs bg-gradient-to-r from-blue-400 via-purple-400 to-orange-400 bg-clip-text text-transparent"><?php echo e($solMages); ?></span>
            
        </div>
        <div class="flex items-center space-x-1">
            <img src="<?php echo e(asset('assets/race/raceSolarPriest.png')); ?>" alt="Жрец" class="w-4 h-4">
            <span class="text-xs text-green-400"><?php echo e($solKnights); ?></span> 
        </div>
    </div>

    

    
    <div class="flex items-center space-x-1">
        <div class="flex items-center space-x-1">
            <span class="text-xs text-red-400"><?php echo e($lunWarriors); ?></span> 
            <img src="<?php echo e(asset('assets/race/raceLunarWarrior.png')); ?>" alt="Воин" class="w-4 h-4">
        </div>
        <div class="flex items-center space-x-1">
            <span
                class="text-xs bg-gradient-to-r from-blue-400 via-purple-400 to-orange-400 bg-clip-text text-transparent"><?php echo e($lunMages); ?></span>
            
            <img src="<?php echo e(asset('assets/race/raceLunarMage.png')); ?>" alt="Маг" class="w-4 h-4">
        </div>
        <div class="flex items-center space-x-1">
            <span class="text-xs text-green-400"><?php echo e($lunKnights); ?></span> 
            <img src="<?php echo e(asset('assets/race/raceLunarPriest.png')); ?>" alt="Жрец" class="w-4 h-4">
        </div>
        <img src="<?php echo e(asset('assets/race/Racelunarius.png')); ?>" alt="Лунариус" class="w-5 h-5">
    </div>

   
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/faction-status.blade.php ENDPATH**/ ?>