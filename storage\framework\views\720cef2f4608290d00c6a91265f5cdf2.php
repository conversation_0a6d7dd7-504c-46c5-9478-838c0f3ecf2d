<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['hasUnreadMessages' => false, 'unreadMessagesCount' => 0, 'hasBrokenItems' => false, 'brokenItemsCount' => 0, 'hasUnreadTicketComments' => false, 'unreadTicketCommentsCount' => 0]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['hasUnreadMessages' => false, 'unreadMessagesCount' => 0, 'hasBrokenItems' => false, 'brokenItemsCount' => 0, 'hasUnreadTicketComments' => false, 'unreadTicketCommentsCount' => 0]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="flex space-x-1">
    
    <?php if($hasUnreadMessages): ?>
        <a href="<?php echo e(route('messages.index')); ?>" class="relative group">
            <div
                class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                <img src="<?php echo e(asset('assets/notification/message.png')); ?>" alt="Сообщения"
                    class="w-5 h-5 message-notification-glow transition-all duration-300">
            </div>
            <?php if($unreadMessagesCount > 0): ?>
                <span
                    class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center animate-pulse">
                    <?php echo e($unreadMessagesCount); ?>

                </span>
            <?php endif; ?>
        </a>
    <?php endif; ?>

    
    <?php if($hasUnreadTicketComments): ?>
        <button id="ticket-notifications-btn" class="relative group">
            <img src="<?php echo e(asset('assets/notification/systemMessages.png')); ?>" alt="Уведомления о тикетах"
                class="w-6 h-6 ticket-notification-pulse transition-all duration-300 hover:brightness-125">
        </button>
    <?php endif; ?>

    
    <?php if($hasBrokenItems): ?>
        <a href="<?php echo e(route('user.equipment')); ?>" class="relative group">
            <div class="flex items-center justify-center">
                <img src="<?php echo e(asset('assets/notification/brokenItems.png')); ?>" alt="Сломанные предметы"
                    class="w-6 h-6 animate-pulse filter hover:brightness-125 transition-all duration-300"
                    style="animation: brokenItemPulse 2s infinite;">
            </div>
            <span
                class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center animate-pulse">
                <?php echo e($brokenItemsCount); ?>

            </span>
        </a>
    <?php endif; ?>
</div>

<style>
    @keyframes brokenItemPulse {
        0% {
            filter: drop-shadow(0 0 0 rgba(220, 38, 38, 0));
        }

        50% {
            filter: drop-shadow(0 0 5px rgba(220, 38, 38, 0.7));
        }

        100% {
            filter: drop-shadow(0 0 0 rgba(220, 38, 38, 0));
        }
    }
</style><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/notifications-bar.blade.php ENDPATH**/ ?>