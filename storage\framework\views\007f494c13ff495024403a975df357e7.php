<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['onlineCount' => 0]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['onlineCount' => 0]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<a href="/online"
    class="group relative flex items-center px-3 py-2 bg-gradient-to-br from-[#2a2621] to-[#201d18] rounded-md border border-[#514b3c] hover:border-[#a6925e] transition-all duration-300 overflow-hidden">
    
    <div class="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-[#a6925e] to-transparent opacity-50">
    </div>
    <div class="absolute right-0 top-0 h-full w-1 bg-gradient-to-b from-[#a6925e] to-transparent opacity-50">
    </div>
    <div class="absolute inset-x-1 top-0 h-0.5 bg-[#a6925e] opacity-30"></div>
    <div class="absolute inset-x-1 bottom-0 h-0.5 bg-[#a6925e] opacity-30"></div>

    
    <div
        class="flex items-center justify-center w-7 h-7 rounded-full bg-[#252117] border border-[#514b3c] mr-2 group-hover:border-[#a6925e] transition-all duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-[#e5b769]" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="3"></circle>
            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
    </div>

    
    <div class="flex flex-col">
        <span class="text-[10px] text-[#9a9483]">Онлайн:</span>
        <span
            class="text-[#7cfc00] text-xs font-medium group-hover:text-[#90ff36] transition-colors duration-300"><?php echo e($onlineCount); ?>

            <span class="text-[10px] opacity-60">игроков</span></span>
    </div>

    
    <div
        class="ml-auto transform translate-x-1 group-hover:translate-x-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-[#e5b769]" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
    </div>
</a><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/online-status-card.blade.php ENDPATH**/ ?>