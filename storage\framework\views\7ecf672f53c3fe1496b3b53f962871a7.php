<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'user' => null,
    'targetResource' => null,
    'targetMob' => null,
    'targetBot' => null,
    'target' => null,
    'isStunned' => false,
    'lastAttacker' => null,
    'lastAttackerResources' => null,
    'routePrefix' => 'battle.mines.custom',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'user' => null,
    'targetResource' => null,
    'targetMob' => null,
    'targetBot' => null,
    'target' => null,
    'isStunned' => false,
    'lastAttacker' => null,
    'lastAttackerResources' => null,
    'routePrefix' => 'battle.mines.custom',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Определяем какое изображение использовать в зависимости от наличия цели
    // Если нет цели (показывается кнопка "Атаковать случайную цель") - используем action-non-target.png
    // Если есть цель - используем action-bg.png
    $currentTargetType = $user->current_target_type;
    $hasActiveTarget = !empty($currentTargetType) && 
                       in_array($currentTargetType, ['resource', 'mob', 'player', 'bot']) && 
                       ($targetResource !== null || $targetMob !== null || $targetBot !== null || $target !== null);
    $backgroundImage = $hasActiveTarget ? 'assets/UI/action-bg.png' : 'assets/UI/action-non-target.png';
?>



<div class="relative bg-contain bg-center bg-no-repeat max-w-xs mx-auto"
    style="background-image: url('<?php echo e(asset($backgroundImage)); ?>'); min-height: 120px; background-size: 100% 100%;">
    
    <div class="text-center py-4 text-[#e9d5a0] font-bold text-sm relative z-10">
        Действия
    </div>

    <div class="p-4 space-y-2 relative z-10">
        <?php if($isStunned): ?>
            <div class="bg-yellow-900/50 text-yellow-400 p-4 rounded-lg text-center my-4 animate-pulse">
                ⚡ Вы оглушены и не можете действовать! ⚡
            </div>
        <?php else: ?>
            <?php if($user->current_target_type === 'resource' && $targetResource): ?>
                
                <div class="space-y-1.5">
                    <div class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        <img src="<?php echo e($targetResource->resource->icon_path); ?>"
                            alt="<?php echo e($targetResource->resource->name ?? 'Ресурс'); ?>" 
                            class="w-6 h-6 rounded-md object-cover"
                            onerror="this.src='<?php echo e(asset('assets/resources/EtherealOrb.png')); ?>'">
                        <div class="ml-1.5 flex-1">
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                <?php echo e($targetResource->resource->name ?? 'Ресурс'); ?>

                                <span class="text-[10px] text-[#9c8d69]">
                                    <?php echo e($targetResource->durability ?? 100); ?>%
                                </span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    <?php
                                        $durabilityPercent = $targetResource->durability ?? 100;
                                        $durabilityColor = $durabilityPercent > 70 ? '#4CAF50' : ($durabilityPercent > 35 ? '#FFC107' : '#F44336');
                                    ?>
                                    <div class="h-full" style="width: <?php echo e($durabilityPercent); ?>%; background-color: <?php echo e($durabilityColor); ?>;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="attackForm" action="<?php echo e(route($routePrefix . '.hit-resource', request()->route('slug'))); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <button id="attackButton" type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?>

                            class="w-full py-2 bg-gradient-to-b from-[#607d4a] to-[#3a5a2c] border-2 border-[#8bae6f]
                            rounded-md text-white text-sm font-bold shadow-md
                            <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#6b8a53] hover:to-[#44653c] active:from-[#526b3f] active:to-[#2f4a24]'); ?> transition-all
                            text-center">
                            <?php echo e($isStunned ? 'Оглушен: действие невозможно' : 'Добыть'); ?>

                        </button>
                    </form>
                </div>
            <?php elseif($user->current_target_type === 'mob' && $targetMob): ?>
                
                <div class="space-y-1.5">
                    <div class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        <img src="<?php echo e(asset($targetMob->icon ?? $targetMob->image_path ?? 'assets/mobs/default.png')); ?>" alt="<?php echo e($targetMob->name); ?>"
                            class="w-6 h-6 rounded-md">
                        <div class="ml-1.5 flex-1">
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                <?php echo e($targetMob->name); ?>

                                <span class="text-[10px] text-[#9c8d69]">
                                    <?php echo e($targetMob->current_hp ?? $targetMob->hp); ?>/<?php echo e($targetMob->max_hp ?? $targetMob->hp); ?>

                                </span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    <?php
                                        $hpPercent = (($targetMob->current_hp ?? $targetMob->hp) / ($targetMob->max_hp ?? $targetMob->hp)) * 100;
                                        $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                                    ?>
                                    <div class="h-full" style="width: <?php echo e($hpPercent); ?>%; background-color: <?php echo e($hpColor); ?>;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="attackForm" action="<?php echo e(route($routePrefix . '.attack-mob', request()->route('slug'))); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <button id="attackButton" type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?>

                            class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777]
                            rounded-md text-white text-sm font-bold shadow-md
                            <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]'); ?> transition-all
                            text-center">
                            <?php echo e($isStunned ? 'Оглушен: действие невозможно' : 'Атаковать'); ?>

                        </button>
                    </form>
                </div>
            <?php elseif($user->current_target_type === 'bot' && $targetBot): ?>
                
                <div class="space-y-1.5">
                    <div class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        <?php
                            // Определяем иконку бота на основе расы и класса
                            $botIcon = $targetBot->image_path ?? 'assets/bots/default.png';
                            if ($targetBot->race === 'solarius' && $targetBot->class === 'warrior') {
                                $botIcon = 'assets/bots/sol_warrior.png';
                            } elseif ($targetBot->race === 'solarius' && $targetBot->class === 'mage') {
                                $botIcon = 'assets/bots/sol_mage.png';
                            } elseif ($targetBot->race === 'lunarius' && $targetBot->class === 'warrior') {
                                $botIcon = 'assets/bots/lun_warrior.png';
                            } elseif ($targetBot->race === 'lunarius' && $targetBot->class === 'mage') {
                                $botIcon = 'assets/bots/lun_mage.png';
                            }
                        ?>
                        <img src="<?php echo e(asset($botIcon)); ?>" alt="<?php echo e($targetBot->name); ?>" class="w-6 h-6 rounded-md">
                        <div class="ml-1.5 flex-1">
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                <?php echo e($targetBot->name); ?>

                                <span class="text-[10px] text-[#9c8d69]">
                                    <?php echo e($targetBot->hp); ?>/<?php echo e($targetBot->max_hp ?? $targetBot->hp); ?>

                                </span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    <?php
                                        $hpPercent = ($targetBot->hp / ($targetBot->max_hp ?? $targetBot->hp)) * 100;
                                        $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                                    ?>
                                    <div class="h-full" style="width: <?php echo e($hpPercent); ?>%; background-color: <?php echo e($hpColor); ?>;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="attackForm" action="<?php echo e(route($routePrefix . '.attack-bot', request()->route('slug'))); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <button id="attackButton" type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?>

                            class="w-full py-2 bg-gradient-to-b from-[#8a5e8a] to-[#6a3c6a] border-2 border-[#b579b5]
                            rounded-md text-white text-sm font-bold shadow-md
                            <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#9a6a9a] hover:to-[#7a487a] active:from-[#7a527a] active:to-[#5a305a]'); ?> transition-all
                            text-center">
                            <?php echo e($isStunned ? 'Оглушен: действие невозможно' : 'Атаковать'); ?>

                        </button>
                    </form>
                </div>
            <?php elseif($user->current_target_type === 'player' && $target): ?>
                
                <div class="space-y-1.5">
                    <div class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        <div class="w-6 h-6 rounded-md bg-gradient-to-b from-[#913838] to-[#762323] border border-[#c07777] flex items-center justify-center text-white font-bold text-xs">
                            PvP
                        </div>
                        <div class="ml-1.5 flex-1">
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                <?php echo e($target->name); ?>

                                <span class="text-[10px] text-red-400">
                                    <?php
                                        // Получаем актуальные ресурсы игрока-цели из Redis
                                        try {
                                            $targetActualResources = $target->profile->getActualResources();
                                            $targetCurrentHp = $targetActualResources['current_hp'];
                                        } catch (\Exception $e) {
                                            // При ошибке используем значения из БД
                                            $targetCurrentHp = $target->profile->current_hp ?? $target->profile->hp;
                                        }
                                    ?>
                                    ❤️ <?php echo e($targetCurrentHp); ?>

                                </span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    <?php
                                        $targetMaxHp = $target->profile->max_hp;
                                        $hpPercent = ($targetCurrentHp / $targetMaxHp) * 100;
                                        $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
                                    ?>
                                    <div class="h-full" style="width: <?php echo e($hpPercent); ?>%; background-color: <?php echo e($hpColor); ?>;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="attackForm" action="<?php echo e(route($routePrefix . '.attack-player', request()->route('slug'))); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <button id="attackButton" type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?>

                            class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777]
                            rounded-md text-white text-sm font-bold shadow-md
                            <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]'); ?> transition-all
                            text-center">
                            <?php echo e($isStunned ? 'Оглушен: действие невозможно' : 'Атаковать'); ?>

                        </button>
                    </form>
                </div>
            <?php else: ?>
                
                <form action="<?php echo e(route($routePrefix . '.attack-any-player', request()->route('slug'))); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <button type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?>

                        class="w-full py-2 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                        <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]'); ?> transition-all
                        text-center">
                        <?php echo e($isStunned ? 'Оглушен: действие невозможно' : 'Бить любого'); ?>

                    </button>
                </form>
            <?php endif; ?>

            
            <div class="pt-1 space-y-1.5">
                <?php
                    // Получаем текущего пользователя
                    $currentUser = $user;

                    // Проверяем последнего атакующего с учетом активности и местоположения
                    $validLastAttacker = null;
                    $validLastAttackerResources = null;

                    if ($currentUser->last_attacker_id) {
                        $candidateAttacker = \App\Models\User::where('id', $currentUser->last_attacker_id)
                            ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
                            ->with('profile', 'statistics')
                            ->first();

                        if ($candidateAttacker) {
                            // Используем улучшенную логику проверки локации
                            $locationService = app(\App\Services\battle\UserLocationService::class);
                            $areInSameLocation = $locationService->arePlayersInSameLocation($currentUser, $candidateAttacker);

                            if ($areInSameLocation) {
                                $validLastAttacker = $candidateAttacker;
                                $validLastAttackerResources = $validLastAttacker->profile->getActualResources();
                            }
                        }
                    }

                    // Проверяем, не выбран ли уже атакующий в качестве цели
                    $isAttackerAlreadyTargeted = $validLastAttacker &&
                        $currentUser->current_target_type === 'player' &&
                        $currentUser->current_target_id == $validLastAttacker->id;
                ?>

                <?php if($validLastAttacker && $validLastAttackerResources && $validLastAttackerResources['current_hp'] > 0 && !$isAttackerAlreadyTargeted): ?>
                    <form action="<?php echo e(route($routePrefix . '.retaliate', request()->route('slug'))); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <button type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?>

                            class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323]
                                border-2 border-[#c07777] rounded-md text-white text-xs font-semibold shadow-md
                                <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]'); ?>

                                transition-all duration-300 text-center group relative overflow-hidden">

                            
                            <span class="absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b6b40] to-transparent opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>

                            <div class="flex items-center justify-center relative z-10">
                                
                                <span class="mr-1 text-red-300">⚔️</span>

                                
                                <span class="font-bold">Бить в ответ</span>

                                
                                <div class="ml-2 flex items-center bg-[#6b2c2c] px-2 py-0.5 rounded border border-[#c07777] shadow-inner">
                                    <span class="text-[10px] text-yellow-200 mr-1"><?php echo e($validLastAttacker->name); ?></span>

                                    
                                    <div class="w-14 h-1.5 bg-[#421a1a] rounded-full overflow-hidden flex-shrink-0">
                                        <div class="<?php echo e($validLastAttackerResources['current_hp'] > $validLastAttacker->profile->max_hp * 0.7 ? 'bg-green-600' : ($validLastAttackerResources['current_hp'] > $validLastAttacker->profile->max_hp * 0.3 ? 'bg-yellow-500' : 'bg-red-600')); ?> h-full"
                                            style="width: <?php echo e(($validLastAttackerResources['current_hp'] / $validLastAttacker->profile->max_hp) * 100); ?>%;">
                                        </div>
                                    </div>

                                    
                                    <span class="text-[9px] text-gray-300 ml-1">
                                        <?php echo e($validLastAttackerResources['current_hp']); ?><span class="text-gray-500">/<?php echo e($validLastAttacker->profile->max_hp); ?></span>
                                    </span>
                                </div>
                            </div>
                        </button>
                    </form>
                <?php endif; ?>

                
                <?php if(($targetResource && $targetResource->resource) || ($user->current_target_type === 'mob' && $targetMob) || ($user->current_target_type === 'bot' && $targetBot) || ($user->current_target_type === 'player' && $target)): ?>
                    <form action="<?php echo e(route($routePrefix . '.change_target', request()->route('slug'))); ?>" method="POST" onsubmit="return handleChangeTargetSubmit(this)">
                        <?php echo csrf_field(); ?>
                        <button type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?>

                            class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md
                            <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b]'); ?> transition-all
                            text-center change-target-btn">
                            <span class="button-text">Сменить цель</span>
                        </button>
                    </form>
                <?php endif; ?>
            </div>

        <?php endif; ?>
    </div>
</div>



<script>
// Проверяем, не определена ли уже функция
if (typeof handleChangeTargetSubmit === 'undefined') {
    let lastChangeTargetTime = 0;
    const CHANGE_TARGET_COOLDOWN = 1000; // 1 секунда

    function handleChangeTargetSubmit(form) {
        const currentTime = Date.now();
        const timeSinceLastChange = currentTime - lastChangeTargetTime;

        if (timeSinceLastChange < CHANGE_TARGET_COOLDOWN) {
            const remainingTime = Math.ceil((CHANGE_TARGET_COOLDOWN - timeSinceLastChange) / 1000);

            // Показываем сообщение об ошибке
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-md shadow-lg z-50';
            errorDiv.textContent = `Подождите ${remainingTime} сек. перед сменой цели`;
            document.body.appendChild(errorDiv);

            // Удаляем сообщение через 3 секунды
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 3000);

            return false; // Предотвращаем отправку формы
        }

        // Обновляем время последней смены цели
        lastChangeTargetTime = currentTime;

        // Блокируем кнопку и показываем индикатор загрузки
        const button = form.querySelector('button[type="submit"]');
        const buttonText = button.querySelector('.button-text');

        if (button && buttonText) {
            button.disabled = true;
            button.classList.add('opacity-50', 'cursor-not-allowed');
            buttonText.textContent = 'Смена цели...';

            // Разблокируем кнопку через 2 секунды на случай ошибки
            setTimeout(() => {
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                buttonText.textContent = 'Сменить цель';
            }, 2000);
        }

        return true; // Разрешаем отправку формы
    }
}
</script>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mines/target-actions.blade.php ENDPATH**/ ?>