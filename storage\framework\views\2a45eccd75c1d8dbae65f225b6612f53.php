<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['userProfile', 'experienceProgress' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['userProfile', 'experienceProgress' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>


<div class="w-full bg-[#6b6658] mt-0.5 h-0.5 rounded-full mb-5">
    <?php if($experienceProgress && isset($experienceProgress['percentage'])): ?>
        <div class="bg-[#e5b769] h-0.5 rounded-full" style="width: <?php echo e($experienceProgress['percentage']); ?>%;"></div>
    <?php else: ?>
        <div class="bg-[#e5b769] h-0.5 rounded-full" style="width: 50%;"></div>
    <?php endif; ?>
    <div class="flex gap-1 items-center ">
        
        <div class="flex items-center ">
            <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Золото" class="w-4 h-4">
            <span class="text-sm font-medium text-[#e5b769]">
                <?php echo e(number_format($userProfile->gold, 0, ',', ' ')); ?>

            </span>
        </div>
        
        <div class="flex items-center ">
            <img src="<?php echo e(asset('assets/silverIcon.png')); ?>" alt="Серебро" class="w-4 h-4">
            <span class="text-sm font-medium text-[#c0c0c0]">
                <?php echo e(number_format($userProfile->silver, 0, ',', ' ')); ?>

            </span>
        </div>
        
        <div class="flex items-center ">
            <img src="<?php echo e(asset('assets/bronzeIcon.png')); ?>" alt="Бронза" class="w-4 h-4">
            <span class="text-sm font-medium text-[#cd7f32]">
                <?php echo e(number_format($userProfile->bronze, 0, ',', ' ')); ?>

            </span>
        </div>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/currency-display.blade.php ENDPATH**/ ?>