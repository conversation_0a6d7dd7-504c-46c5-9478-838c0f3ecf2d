<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Персонаж: <?php echo e(Auth::user()->name); ?></title>

    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js', 'resources/css/user-profile.css', 'resources/css/experience-progress.css', 'resources/js/user/profile-interactions.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    
    <div class="container max-w-md mx-auto px-1 py-0 border-2 border-[#a6925e] rounded-lg flex-grow">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <?php if(Auth::check()): ?>
            <?php
                $guildInvitation = Auth::user()->getLatestGuildInvitation();
            ?>
            <?php if (isset($component)) { $__componentOriginalf073577b00d5eaef97c81e52a3000637 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf073577b00d5eaef97c81e52a3000637 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.guild-invitation','data' => ['guildInvitation' => $guildInvitation]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.guild-invitation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['guildInvitation' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($guildInvitation)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf073577b00d5eaef97c81e52a3000637)): ?>
<?php $attributes = $__attributesOriginalf073577b00d5eaef97c81e52a3000637; ?>
<?php unset($__attributesOriginalf073577b00d5eaef97c81e52a3000637); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf073577b00d5eaef97c81e52a3000637)): ?>
<?php $component = $__componentOriginalf073577b00d5eaef97c81e52a3000637; ?>
<?php unset($__componentOriginalf073577b00d5eaef97c81e52a3000637); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <div class="w-full mx-auto">
            <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
            
            <div class="px-4 py-0.5">
                <hr class="border-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-70">
            </div>
            
            <?php if (isset($component)) { $__componentOriginalc249f68c23236e1f9e731ca34e4bd461 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc249f68c23236e1f9e731ca34e4bd461 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.location-name','data' => ['title' => 'Персонаж']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.location-name'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Персонаж']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc249f68c23236e1f9e731ca34e4bd461)): ?>
<?php $attributes = $__attributesOriginalc249f68c23236e1f9e731ca34e4bd461; ?>
<?php unset($__attributesOriginalc249f68c23236e1f9e731ca34e4bd461); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc249f68c23236e1f9e731ca34e4bd461)): ?>
<?php $component = $__componentOriginalc249f68c23236e1f9e731ca34e4bd461; ?>
<?php unset($__componentOriginalc249f68c23236e1f9e731ca34e4bd461); ?>
<?php endif; ?>
        </div>

        
        <?php if(isset($canReturnToBattle) && $canReturnToBattle && isset($lastBattleRoute) && $lastBattleRoute): ?>
            <div class="mb-2">
                <a href="<?php echo e(url($lastBattleRoute)); ?>"
                    class="block w-full max-w-xs mx-auto px-2 py-2 text-lg font-semibold text-[#c4a46b]
                                                                                                                                                           bg-gradient-to-b from-[#2b2a21] to-[#1d1c17] border border-[#8c784e]
                                                                                                                                                           rounded-md uppercase tracking-wide text-center
                                                                                                                                                           hover:border-[#b59e70] hover:text-[#dfc590] hover:shadow-md
                                                                                                                                                           transition-all duration-300">
                    ⚔️ ВЕРНУТЬСЯ В БОЙ ⚔️
                </a>
            </div>
        <?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal6fc2934a0cc4bc0e6b375c66c7191fe9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6fc2934a0cc4bc0e6b375c66c7191fe9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.content-block','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.content-block'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>

            
            
            <div class="text-center mb-2 relative"> 

                
                <div class="text-xs text-[#a09a8a] mb-1">ID: <?php echo e($user->id); ?></div>
                
                <h1 class="text-2xl text-[#e5b769] font-bold  tracking-wide "><?php echo e($user->name); ?></h1>
                
                <div
                    class="absolute -top-0 -right-2 w-10 h-10 rounded-full bg-gradient-to-b from-[#4a4a3d] to-[#2b2a21] border-2 border-[#a6925e] flex items-center justify-center shadow-md">
                    <span class="text-[#e5b769] text-sm font-bold"><?php echo e($userProfile->level ?? 1); ?></span>
                    
                </div>
                
                <p class="text-[#d9d3b8] flex items-center justify-center gap-3 mt-1">
                    <span
                        class="px-2.5 py-0.5 bg-[#38352c] rounded-md border border-[#514b3c] text-sm shadow-sm flex items-center">
                        <?php if($userProfile->race === 'lunarius'): ?>
                            <img src="<?php echo e(asset('assets/race/Racelunarius.png')); ?>" alt="Лунариус" class="w-5 h-5">
                        <?php elseif($userProfile->race === 'solarius'): ?>
                            <img src="<?php echo e(asset('assets/race/Racesolarius.png')); ?>" alt="Солариус" class="w-5 h-5">
                        <?php endif; ?>
                    </span>
                    <span
                        class="px-2.5 py-0.5 bg-[#38352c] rounded-md border border-[#514b3c] text-sm shadow-sm"><?php echo e($userProfile->class ?? 'Не указан'); ?></span>
                    
                </p>
            </div>

            
            
            <div class="mb-3">
                <?php if (isset($component)) { $__componentOriginalf3624f27aebdbcef5d37a71040fab388 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf3624f27aebdbcef5d37a71040fab388 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.experience-progress-bar','data' => ['experienceProgress' => $experienceProgress ?? null,'type' => 'thick','showText' => true,'showLevel' => true,'isMaxLevel' => isset($experienceProgress['is_max_level']) ? $experienceProgress['is_max_level'] : false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.experience-progress-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null),'type' => 'thick','showText' => true,'showLevel' => true,'isMaxLevel' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(isset($experienceProgress['is_max_level']) ? $experienceProgress['is_max_level'] : false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf3624f27aebdbcef5d37a71040fab388)): ?>
<?php $attributes = $__attributesOriginalf3624f27aebdbcef5d37a71040fab388; ?>
<?php unset($__attributesOriginalf3624f27aebdbcef5d37a71040fab388); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf3624f27aebdbcef5d37a71040fab388)): ?>
<?php $component = $__componentOriginalf3624f27aebdbcef5d37a71040fab388; ?>
<?php unset($__componentOriginalf3624f27aebdbcef5d37a71040fab388); ?>
<?php endif; ?>
            </div>

            
            
            <div class="bg-[#2a271c] border border-[#514b3c] rounded-lg p-3 shadow-lg mb-2 relative">
                
                <div class="text-center relative flex items-center justify-center">

                    <div>
                        <span class="text-[#d9d3b8] text-sm uppercase tracking-wider block">Боевая мощь</span>
                        
                        
                        <div class="text-[#e5b769] text-2xl font-bold leading-tight"><?php echo e($gs ?? 0); ?></div>
                        
                    </div>
                </div>
            </div>

            





            
            
            <div class="relative flex flex-col items-center mb-4">
                
                <div class="relative flex items-center justify-center mb-3">
                    
                    <div class="flex flex-col space-y-2.5 mr-3">
                        
                        
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">


                            <?php if(isset($equippedItems['шлем'])): ?>
                                <?php
                                    $gameItem = $equippedItems['шлем']; // Экземпляр GameItem
                                    $item = $gameItem->item; // Связанная модель Item
                                ?>
                                <?php if($item): ?>
                                    
                                    
                                    <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                        class="block w-11 h-11"> 
                                        
                                        <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                            alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                            class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110" />
                                    </a>
                                    
                                <?php else: ?>
                                    
                                    <img src="<?php echo e(asset('assets/helmetIcon.png')); ?>" alt="Ошибка загрузки предмета"
                                        class="w-11 h-11 object-cover opacity-50" />
                                <?php endif; ?>
                            <?php else: ?>
                                
                                <img src="<?php echo e(asset('assets/helmetIcon.png')); ?>" alt="Пустой слот шлем"
                                    class="w-11 h-11 object-cover opacity-30" />
                                
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            <?php if(isset($equippedItems['тело'])): ?>
                                <?php
                                    $gameItem = $equippedItems['тело'];
                                    $item = $gameItem->item;
                                ?>
                                <?php if($item): ?>
                                    
                                    <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                        class="block w-11 h-11">
                                        <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                            alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                            class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110" />
                                    </a>
                                    
                                <?php else: ?>
                                    
                                    <img src="<?php echo e(asset('assets/armorIcon.png')); ?>" alt="Ошибка загрузки предмета"
                                        class="w-11 h-11 object-cover opacity-50" />
                                <?php endif; ?>
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/armorIcon.png')); ?>" alt="Пустой слот броня"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            <?php if(isset($equippedItems['перчатки']) && $equippedItems['перчатки']->item): ?>
                                <?php
                                    $gameItem = $equippedItems['перчатки'];
                                    $item = $gameItem->item;
                                ?>
                                
                                <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                    class="block w-11 h-11">
                                    <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                        alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                        class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                                
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/glovesIcon.png')); ?>" alt="Пустой слот перчатки"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            <?php if(isset($equippedItems['обувь']) && $equippedItems['обувь']->item): ?>
                                <?php
                                    $gameItem = $equippedItems['обувь'];
                                    $item = $gameItem->item;
                                ?>
                                
                                <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                    class="block w-11 h-11">
                                    <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                        alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                        class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                                
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/bootsIcon.png')); ?>" alt="Пустой слот обувь"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    
                    <div
                        class="w-44 h-64 bg-[#38352c] border-2 border-[#514b3c] rounded-lg flex items-center justify-center shadow-lg relative overflow-hidden">
                        
                        <div
                            class="absolute top-1 left-1 w-4 h-4 border-t-2 border-l-2 border-[#a6925e]/50 rounded-tl-md">
                        </div>
                        <div
                            class="absolute top-1 right-1 w-4 h-4 border-t-2 border-r-2 border-[#a6925e]/50 rounded-tr-md">
                        </div>
                        <div
                            class="absolute bottom-1 left-1 w-4 h-4 border-b-2 border-l-2 border-[#a6925e]/50 rounded-bl-md">
                        </div>
                        <div
                            class="absolute bottom-1 right-1 w-4 h-4 border-b-2 border-r-2 border-[#a6925e]/50 rounded-br-md">
                        </div>

                        
                        <?php
                            $avatarPath = 'assets/character-placeholder.png'; // Заглушка по умолчанию
                            if ($userProfile->race === 'solarius') {
                                $avatarPath = 'assets/avatar/hero-solarius.jpg';
                            } elseif ($userProfile->race === 'lunarius') {
                                $avatarPath = 'assets/avatar/hero-lunarius.jpg';
                            }
                        ?>
                        <img src="<?php echo e(asset($avatarPath)); ?>"
                            alt="Персонаж <?php echo e(getRaceDisplayName($userProfile->race ?? 'solarius')); ?>"
                            class="absolute inset-0 w-full h-full object-cover rounded-lg" />

                        
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 z-10">
                            <span
                                class="text-[#d9d3b8] text-xs opacity-70 cursor-pointer hover:opacity-100 hover:text-[#e5b769] transition-all duration-200 bg-black bg-opacity-50 px-2 py-1 rounded">
                                сменить
                            </span>
                        </div>
                    </div>

                    
                    <div class="flex flex-col space-y-2.5 ml-3">
                        
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            <?php if(isset($equippedItems['браслет']) && $equippedItems['браслет']->item): ?>
                                <?php
                                    $gameItem = $equippedItems['браслет'];
                                    $item = $gameItem->item;
                                ?>
                                
                                <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                    class="block w-11 h-11">
                                    <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                        alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                        class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                                
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/bracerIcon.png')); ?>" alt="Пустой слот браслет"
                                    class="w-11 h-11 object-cover opacity-30" />
                                
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            <?php if(isset($equippedItems['пояс']) && $equippedItems['пояс']->item): ?>
                                <?php
                                    $gameItem = $equippedItems['пояс'];
                                    $item = $gameItem->item;
                                ?>
                                
                                <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                    class="block w-11 h-11">
                                    <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                        alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                        class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                                
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/beltIcon.png')); ?>" alt="Пустой слот пояс"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">


                            <?php if(isset($equippedItems['оружие']) && $equippedItems['оружие']->item): ?>
                                <?php
                                    $gameItem = $equippedItems['оружие'];
                                    $item = $gameItem->item;
                                ?>
                                
                                <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                    class="block w-11 h-11">
                                    <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                        alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                        class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                                
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/weaponIcon.png')); ?>" alt="Пустой слот оружие"
                                    class="w-11 h-11 object-cover opacity-45" /> 
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            <?php if(isset($equippedItems['доп оружие']) && $equippedItems['доп оружие']->item): ?>
                                <?php
                                    $gameItem = $equippedItems['доп оружие'];
                                    $item = $gameItem->item;
                                ?>
                                
                                <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                    class="block w-11 h-11">
                                    <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                        alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                        class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                                
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/weapon2Icon.png')); ?>" alt="Пустой слот доп. оружие"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                
                
                <div class="grid grid-cols-3 gap-3">
                    
                    
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        <?php if(isset($equippedItems['кольцо 1']) && $equippedItems['кольцо 1']->item): ?>
                            <?php
                                $gameItem = $equippedItems['кольцо 1'];
                                $item = $gameItem->item;
                            ?>
                            
                            <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                class="block w-11 h-11">
                                
                                <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                    alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                    class="w-full h-full object-cover transform hover:scale-110 transition-transform duration-300" />
                            </a>
                            
                        <?php else: ?>
                            
                            <img src="<?php echo e(asset('assets/ringIcon.png')); ?>" alt="Пустой слот кольцо 1"
                                class="w-11 h-11 object-cover opacity-30" />
                            
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        <?php endif; ?>
                        
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                    
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        <?php if(isset($equippedItems['кольцо 2']) && $equippedItems['кольцо 2']->item): ?>
                            <?php
                                $gameItem = $equippedItems['кольцо 2'];
                                $item = $gameItem->item;
                            ?>
                            
                            <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                class="block w-11 h-11">
                                <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                    alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                    class="w-full h-full object-cover transform hover:scale-110 transition-transform duration-300" />
                            </a>
                            
                        <?php else: ?>
                            <img src="<?php echo e(asset('assets/ringIcon.png')); ?>" alt="Пустой слот кольцо 2"
                                class="w-11 h-11 object-cover opacity-30" />
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        <?php endif; ?>
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                    
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        <?php if(isset($equippedItems['амулет']) && $equippedItems['амулет']->item): ?>
                            <?php
                                $gameItem = $equippedItems['амулет'];
                                $item = $gameItem->item;
                            ?>
                            
                            <a href="<?php echo e(route('user.equipped-item.details', ['gameItem' => $gameItem->id])); ?>"
                                class="block w-11 h-11">
                                <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                    alt="<?php echo e($item->name ?? 'Предмет'); ?>"
                                    class="w-full h-full object-cover transform hover:scale-110 transition-transform duration-300" />
                            </a>
                            
                        <?php else: ?>
                            <img src="<?php echo e(asset('assets/amuletIcon.png')); ?>" alt="Пустой слот амулет"
                                class="w-11 h-11 object-cover opacity-30" />
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        <?php endif; ?>
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                </div>

                
                
                <div class="mt-5 w-full">
                    
                    <div class="relative mb-3">
                        <div
                            class="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-[#2a271c] px-3 py-1 border border-[#a6925e] rounded-full z-10 shadow-md">
                            <h3 class="text-[#e5b769] text-base font-semibold">Умения</h3> 
                        </div>
                    </div>
                    
                    <div
                        class="grid grid-cols-4 gap-5 bg-[#2a271c] border border-[#514b3c] rounded-lg p-3 shadow-lg mt-2 mx-auto max-w-xs">
                        
                        <?php for($i = 0; $i < 4; $i++): ?>
                                  <div
                                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                                        <?php if($i == 0): ?>
                                            
                                            <img src="<?php echo e(asset('assets/skillFireball.png')); ?>" alt="Огненный шар"
                                                class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                        <?php else: ?>
                                            
                                            <div
                                                class="w-11 h-11 bg-gradient-to-br from-[#4a4a3d] to-[#3b3a33] flex items-center justify-center rounded-md">
                                                <span class="text-[#8c784e] text-2xl">?</span> 
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div
                                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-30">
                                        </div>
                            </div>
                        <?php endfor; ?>
                </div>
            </div>

            
            
            <div class="flex items-center justify-center space-x-3 mt-2.5">
                
                <button
                    class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                    
                    <img src="<?php echo e(asset('assets/iconBook.png')); ?>" alt="умение"
                        class="w-9 h-9 transform group-hover:scale-110 transition-transform duration-300">
                </button>
                
                <a href="<?php echo e(route('user.equipment')); ?>"
                    class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                    
                    <img src="<?php echo e(asset('assets/test1.png')); ?>" alt="снаряжение"
                        class="w-9 h-9 transform group-hover:scale-110 transition-transform duration-300">
                </a>
                
                <a href="<?php echo e(route('messages.index')); ?>"
                    class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                    
                    <?php if($hasUnreadMessages): ?>
                        <img src="<?php echo e(asset('assets/notification/message.png')); ?>" alt="Сообщения"
                            class="w-9 h-9 animate-pulse filter hover:brightness-125 transition-all duration-300 transform group-hover:scale-110"
                            style="animation: messagePulse 2s infinite;">
                    <?php else: ?>
                        <span class="text-[#e5b769] text-2xl">📩</span>
                    <?php endif; ?>
                    
                    <?php if($hasUnreadMessages): ?>
                        <div
                            class="absolute -top-2 -right-2 bg-[#e74c3c] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
                            <?php echo e($unreadMessagesCount > 9 ? '9+' : $unreadMessagesCount); ?>

                        </div>
                    <?php endif; ?>
                </a>
            </div>
    </div>

    

    
    
    <div class="rounded-lg mb-6 shadow-lg relative overflow-hidden"> 
        
        
        <div class="grid grid-cols-1 gap-3">
            
            <div class="bg-[#2a271c] bg-opacity-90 rounded-lg p-3 border border-[#3d3828]">
                
                
                
                <div class="space-y-1"> 
                    
                    <div class="flex items-center">
                        
                        
                        <div class="flex-1"><span class="text-base text-[#d9d3b8]">Сила: </span><span
                                class="text-base text-[#ffc83d] font-medium"><?php echo e($effectiveStats['strength'] ?? 0); ?></span>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        
                        <div class="flex-1"><span class="text-base text-[#d9d3b8]">Интеллект: </span><span
                                class="text-base text-[#ffc83d] font-medium"><?php echo e($effectiveStats['intelligence'] ?? 0); ?></span>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        
                        <div class="flex-1">
                            <span class="text-base text-[#d9d3b8]">Броня: </span>
                            <span
                                class="text-base text-[#ffc83d] font-medium"><?php echo e(round($effectiveStats['armor'] ?? 0)); ?></span>
                            
                            <span
                                class="text-base text-gray-400 ml-1">(<?php echo e($userProfile->getFormattedArmorReduction()); ?>)</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        
                        <div class="flex-1"><span class="text-base text-[#d9d3b8]">Восстановление:
                            </span><span
                                class="text-base text-[#ffc83d] font-medium"><?php echo e($effectiveStats['recovery'] ?? 0); ?></span>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        
                        <div class="flex-1">
                            <span class="text-base text-[#d9d3b8]">Шанс крита: </span>
                            <span
                                class="text-base text-[#ffc83d] font-medium"><?php echo e($effectiveStats['crit_chance'] ?? 0); ?></span>
                            
                            <?php if($userProfile->getFormattedCritChance() > 0): ?>
                                <span
                                    class="text-base text-green-600 ">(<?php echo e($userProfile->getFormattedCritChance()); ?>)</span>
                                
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        
                        <div class="flex-1">
                            <span class="text-base text-[#d9d3b8]">Сила крита: </span>
                            <span
                                class="text-base text-[#ffc83d] font-medium"><?php echo e($effectiveStats['crit_damage'] ?? 0); ?></span>
                            
                            <?php if($userProfile->getFormattedCritDamage() > 0): ?>
                                <span
                                    class="text-base text-green-600 ">(<?php echo e($userProfile->getFormattedCritDamage()); ?>)</span>
                                
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-[#2a271c] bg-opacity-90 rounded-lg p-1.5 border border-[#3d3828]">
                
                
                
                <div class="space-y-1"> 
                    
                    <div class="flex items-center">
                        
                        <img src="<?php echo e(asset('assets/user/hpIcon.png')); ?>" alt="HP" class="w-6 h-6 mr-2.5">
                        
                        <div class="flex-1">
                            
                            <span class="text-base text-[#d9d3b8]">HP: </span>
                            <span id="hp-value" class="text-base text-[#ffc83d] font-medium"
                                data-hp="<?php echo e($actualResources['current_hp'] ?? 0); ?>"
                                data-max-hp="<?php echo e($userProfile->max_hp ?? 0); ?>"
                                data-hp-regen="<?php echo e(isset($regenerationRates['hp_per_second']) ? (float) $regenerationRates['hp_per_second'] : 0); ?>">
                                <?php echo e($actualResources['current_hp'] ?? 0); ?> / <?php echo e($userProfile->max_hp ?? '?'); ?>

                                (<?php echo e(isset($regenerationRates['hp_per_second']) ? (int) $regenerationRates['hp_per_second'] : 0); ?>/сек)
                            </span>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        
                        <img src="<?php echo e(asset('assets/user/mp-icon.png')); ?>" alt="MP" class="w-6 h-6 mr-2.5">
                        
                        <div class="flex-1">
                            
                            <span class="text-base text-[#d9d3b8]">MP: </span>
                            <span id="mp-value" class="text-base text-[#ffc83d] font-medium"
                                data-mp="<?php echo e($actualResources['current_mp'] ?? 0); ?>"
                                data-max-mp="<?php echo e($userProfile->max_mp ?? 0); ?>"
                                data-mp-regen="<?php echo e(isset($regenerationRates['mp_per_second']) ? (float) $regenerationRates['mp_per_second'] : 0); ?>">
                                <?php echo e($actualResources['current_mp'] ?? 0); ?> / <?php echo e($userProfile->max_mp ?? '?'); ?>

                                (<?php echo e(isset($regenerationRates['mp_per_second']) ? (int) $regenerationRates['mp_per_second'] : 0); ?>/сек)
                            </span>
                        </div>
                    </div>
                </div>

                
                <div class="flex justify-between items-center text-[#d9d3b8] rounded-md pt-1.5 shadow-inner mt-2">
                    
                    <div class="flex items-center">
                        <div
                            class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] mr-1">
                            
                            <span class="text-[#e74c3c] text-xs">⚔️</span>
                        </div>
                        <div class="flex flex-col">
                            
                            <span class="text-[#e5b769] text-base">Боевая Активность:
                                <?php echo e(number_format($combatActivity->damage_dealt ?? 0)); ?></span>
                        </div>
                    </div>
                </div>
                
                
                <div class="mt-2">
                    
                    <div class="text-[#d9d3b8] text-base font-medium">Активные эффекты</div>
                    
                    <div class="flex flex-wrap gap-2.5 p-2.5 bg-[#191815] rounded border border-[#3d3828]">
                        <?php if($currentlyActiveEffects->count() > 0): ?>
                            
                            <?php $__currentLoopData = $currentlyActiveEffects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $effect): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex flex-col items-center text-center"
                                    title="<?php echo e($effect->skill->name ?? 'Неизвестный эффект'); ?> (<?php echo e($effect->remaining_duration); ?> сек.)">
                                    <div class="relative">
                                        <div
                                            class="w-9 h-9 rounded-full bg-[#222114] border border-[#a6925e] flex items-center justify-center overflow-hidden">
                                            
                                            <?php
                                                // Получаем путь к иконке эффекта или используем заглушку
                                                $effectIcon = $effect->skill->icon_path ?? asset('assets/skillLightOfFaith.png');
                                            ?>
                                            <img src="<?php echo e($effectIcon); ?>"
                                                alt="<?php echo e($effect->skill->name ?? 'Неизвестный эффект'); ?>"
                                                class="w-7 h-7 object-cover rounded-full">
                                        </div>
                                        <?php if($effect->stacks > 1): ?>
                                            <div
                                                class="absolute -top-1 -right-1 w-4 h-4 bg-[#222114] rounded-full flex items-center justify-center border border-[#a6925e]">
                                                <span class="text-[#e5b769] text-[9px]"><?php echo e($effect->stacks); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <span class="text-[#e5b769] text-xs mt-1 font-medium">
                                        <?php echo e($effect->remaining_duration); ?>с
                                    </span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            
                            <div class="w-full text-center text-[#8c784e] text-sm py-2">
                                Нет активных эффектов
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- <div class="bg-[#2a271c] bg-opacity-90 rounded-lg p-1.5 border border-[#3d3828]"> 
                        
                        
                        <div class="space-y-1"> 
                             
                             <div class="flex items-center">
                                 
                                
                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">Огонь: </span><span class="text-base text-[#ff7b4a] font-medium"><?php echo e($effectiveStats['resistance_fire'] ?? 0); ?>%</span></div>
                            </div>
                             
                            <div class="flex items-center">
                                
                                
                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">Молния: </span><span class="text-base text-[#4aecff] font-medium"><?php echo e($effectiveStats['resistance_lightning'] ?? 0); ?>%</span></div>
                            </div>
                            
                        </div>
                    </div> -->
        </div>
    </div>

    

    
    
    <div class="bg-[#211f1a] border border-[#a6925e] rounded-lg p-4 shadow-lg relative overflow-hidden">
        
        
        <div
            class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
        </div>
        
        
        <div class="grid grid-cols-2 gap-4">
            
            <div class="bg-[#2b2a21] border border-[#514b3c] rounded-lg p-3.5 shadow-sm">
                
                <div class="text-center font-semibold text-[#e5b769] mb-2 text-base">PvP</div>
                
                
                <div class="flex justify-between items-center text-[#d9d3b8] text-sm mb-1">
                    <span>Победы:</span>
                    
                    <span class="text-[#a6e55e] font-medium"><?php echo e($userStatistics->pvp_wins ?? 0); ?></span>
                </div>
                
                <div class="flex justify-between items-center text-[#d9d3b8] text-sm">
                    <span>Поражения:</span>
                    
                    <span class="text-[#e55e5e] font-medium"><?php echo e($userStatistics->pvp_losses ?? 0); ?></span>
                </div>
            </div>
            
            <div class="bg-[#2b2a21] border border-[#514b3c] rounded-lg p-3.5 shadow-sm">
                
                <div class="text-center font-semibold text-[#e5b769] mb-2 text-base">PvE</div>
                
                
                <div class="flex justify-between items-center text-[#d9d3b8] text-sm mb-1">
                    <span>Победы:</span>
                    
                    <span class="text-[#a6e55e] font-medium"><?php echo e($userStatistics->pve_wins ?? 0); ?></span>
                </div>
                
                <div class="flex justify-between items-center text-[#d9d3b8] text-sm">
                    <span>Поражения:</span>
                    
                    <span class="text-[#e55e5e] font-medium"><?php echo e($userStatistics->pve_losses ?? 0); ?></span>
                </div>
            </div>
        </div>
        
        <div
            class="absolute bottom-0 right-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
        </div>
    </div>

    

    
    
    <div class="bg-[#211f1a] border border-[#a6925e] rounded-lg p-4 shadow-lg relative overflow-hidden mb-4">
        
        <div
            class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
        </div>
        

        

        
        <div class="text-[#d9d3b8] text-base"> 

            
            
            <div class="flex items-center"> 
                
                
                <span class="-ml-3 mr-0.5 leading-none 
                            
                            <?php if($userStatus === 'online'): ?> text-green-500 animate-pulse 
                            <?php elseif($userStatus === 'idle'): ?> text-gray-500 
                            <?php else: ?> text-red-500  <?php endif; ?>
                        ">●</span>
                
                
                <div class="font-medium">
                    
                    <?php if($userStatus === 'online'): ?>
                        <span class="text-green-400"><?php echo e($displayLocation ?? 'Онлайн (Неизвестно)'); ?></span>
                    <?php elseif($userStatus === 'idle'): ?>
                        <span class="text-gray-400"><?php echo e($displayLocation ?? 'Неактивен'); ?></span>
                    <?php else: ?>
                        <span class="text-red-500"><?php echo e($displayLocation ?? 'Не в сети (неизвестно)'); ?></span>
                    <?php endif; ?>
                </div>
            </div>

            
            <div class="flex items-center"> 
                
                
                <div class="font-medium">Профессия: <span
                        class="text-[#e5b769]"><?php echo e($userProfile->profession ?? 'Нет'); ?></span></div>
            </div>

            
            <div class="flex items-center"> 
                
                <div class="font-medium">
                    Провел времени: <span class="text-[#e5b769]">
                        
                        <?php echo e($userStatistics->hours_played ?? 0); ?>ч
                        <?php echo e($userStatistics->minutes_played ?? 0); ?>м
                        
                    </span>
                </div>
            </div>
            
            <div class="flex items-center"> 
                
                <div class="font-medium">
                    В игре с: <span class="text-[#e5b769]">
                        
                        <?php echo e($user->formatted_date ?? $user->created_at->format('d.m.Y')); ?>

                    </span>
                </div>
            </div>
        </div>
        
        <div
            class="absolute bottom-0 right-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
        </div>
    </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6fc2934a0cc4bc0e6b375c66c7191fe9)): ?>
<?php $attributes = $__attributesOriginal6fc2934a0cc4bc0e6b375c66c7191fe9; ?>
<?php unset($__attributesOriginal6fc2934a0cc4bc0e6b375c66c7191fe9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6fc2934a0cc4bc0e6b375c66c7191fe9)): ?>
<?php $component = $__componentOriginal6fc2934a0cc4bc0e6b375c66c7191fe9; ?>
<?php unset($__componentOriginal6fc2934a0cc4bc0e6b375c66c7191fe9); ?>
<?php endif; ?>
    </div>

    
    <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/user/profile.blade.php ENDPATH**/ ?>