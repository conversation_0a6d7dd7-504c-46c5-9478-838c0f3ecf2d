

<div
    class="relative flex items-center px-3 py-2 bg-gradient-to-br from-[#2a2621] to-[#201d18] rounded-md border border-[#514b3c]">
    
    <div class="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-[#a6925e] to-transparent opacity-50">
    </div>
    <div class="absolute right-0 top-0 h-full w-1 bg-gradient-to-b from-[#a6925e] to-transparent opacity-50">
    </div>
    <div class="absolute inset-x-1 top-0 h-0.5 bg-[#a6925e] opacity-30"></div>
    <div class="absolute inset-x-1 bottom-0 h-0.5 bg-[#a6925e] opacity-30"></div>

    
    <div class="flex items-center justify-center w-7 h-7 rounded-full bg-[#252117] border border-[#514b3c] mr-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-[#e5b769]" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
        </svg>
    </div>

    
    <div class="flex flex-col">
        <span class="text-[10px] text-[#9a9483]">Время:</span>
        <span class="text-[#d3c6a6] text-xs font-medium" id="server-time">14:33</span>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/time-card.blade.php ENDPATH**/ ?>