

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'potions' => [], // Массив зелий для отображения (должны быть зелья с пояса)
    'maxSlots' => 5, // Максимальное количество слотов (по умолчанию 5 для пояса)
    'showTitle' => false, // Показывать ли заголовок
    'title' => 'Быстрые зелья', // Заголовок компонента
    'compact' => true, // Компактный режим отображения по умолчанию
    'ultraCompact' => false, // Супер-компактный режим (еще меньше)
    'showHotkeys' => true, // Показывать ли горячие клавиши
    'vertical' => false, // Вертикальное расположение вместо горизонтального
    'customHotkeys' => null, // Пользовательские горячие клавиши (массив key => slot_position)
    'tooltips' => true, // Показывать всплывающие подсказки при наведении
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'potions' => [], // Массив зелий для отображения (должны быть зелья с пояса)
    'maxSlots' => 5, // Максимальное количество слотов (по умолчанию 5 для пояса)
    'showTitle' => false, // Показывать ли заголовок
    'title' => 'Быстрые зелья', // Заголовок компонента
    'compact' => true, // Компактный режим отображения по умолчанию
    'ultraCompact' => false, // Супер-компактный режим (еще меньше)
    'showHotkeys' => true, // Показывать ли горячие клавиши
    'vertical' => false, // Вертикальное расположение вместо горизонтального
    'customHotkeys' => null, // Пользовательские горячие клавиши (массив key => slot_position)
    'tooltips' => true, // Показывать всплывающие подсказки при наведении
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Получаем зелья с пояса, если они не были переданы явно
    if (empty($potions) && auth()->check()) {
        $user = auth()->user();

        // Получаем слоты пояса с зельями
        $beltSlots = \App\Models\PotionBeltSlot::where('user_id', $user->id)
            ->orderBy('slot_position')
            ->with('potion')
            ->get();

        // Формируем массив зелий в правильном порядке слотов
        $beltPotions = collect();
        foreach ($beltSlots as $slot) {
            if ($slot->potion) {
                $beltPotions->push($slot->potion);
            }
        }

        $potions = $beltPotions;
    }

    // Заполняем массив слотов зельями или пустыми слотами
    $slots = [];
    for ($i = 1; $i <= $maxSlots; $i++) {
        $potion = $potions->firstWhere('beltSlot.slot_position', $i);
        $slots[$i] = $potion ?: null;
    }

    // Определяем классы для компонента на основе режима отображения
    if ($ultraCompact) {
        $containerClass = 'p-0.5';
        $slotSize = 'w-8 h-8';
        $iconSize = 'w-6 h-6';
        $hotkeyClass = 'text-[8px] bottom-0 left-0';
        $useCountClass = 'w-2.5 h-2.5 text-[7px]';
        $gapSize = 'gap-0.5';
    } elseif ($compact) {
        $containerClass = 'p-1';
        $slotSize = 'w-9 h-9';
        $iconSize = 'w-7 h-7';
        $hotkeyClass = 'text-[9px] bottom-0 left-0';
        $useCountClass = 'w-3 h-3 text-[8px]';
        $gapSize = 'gap-1';
    } else {
        $containerClass = 'p-2';
        $slotSize = 'w-12 h-12';
        $iconSize = 'w-10 h-10';
        $hotkeyClass = 'text-[10px] bottom-0 left-0';
        $useCountClass = 'w-3.5 h-3.5 text-[9px]';
        $gapSize = 'gap-1.5';
    }

    // Соответствие клавиш и слотов
    $hotkeys = [
        1 => '1',
        2 => '2',
        3 => '3',
        4 => '4',
        5 => '5',
    ];

    // Если переданы пользовательские горячие клавиши, используем их
    if ($customHotkeys && is_array($customHotkeys)) {
        $hotkeys = array_merge($hotkeys, $customHotkeys);
    }

    // Определяем ориентацию контейнера
    $containerOrientation = $vertical ? 'flex-col' : '';
?>

<div <?php echo e($attributes->merge(['class' => "bg-[#252117]/80 border border-[#514b3c] rounded-md shadow-sm $containerClass"])); ?>

    id="quick-potion-bar">
    <?php if($showTitle): ?>
        <h3 class="text-[#e5b769] text-xs font-bold text-center mb-0.5"><?php echo e($title); ?></h3>
    <?php endif; ?>

    <div class="flex justify-center <?php echo e($gapSize); ?> <?php echo e($containerOrientation); ?>">
        <?php $__currentLoopData = $slots; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slotPosition => $potion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                // Определяем классы и стили для слота
                $slotClass = "relative $slotSize bg-[#1a1814] rounded-md border flex items-center justify-center";
                $borderClass = $potion ? 'border-[#a6925e]' : 'border-[#514b3c]';
                $isEmpty = !$potion;

                // Определяем цвет качества зелья и класс свечения
                $qualityColors = [
                    'Обычное' => 'text-gray-200',
                    'Улучшенное' => 'text-green-400',
                    'Необычное' => 'text-green-400',
                    'Превосходное' => 'text-blue-400',
                    'Редкое' => 'text-blue-400',
                    'Эпическое' => 'text-purple-400',
                    'Легендарное' => 'text-orange-400',
                ];

                $qualityColor = $potion ? $qualityColors[$potion->quality] ?? 'text-gray-200' : '';
                $glowColorClass = $potion ? str_replace('text-', 'glow-color-', $qualityColor) : '';

                // Определяем путь к иконке зелья
                $iconPath = '';
                if ($potion) {
                    if (method_exists($potion, 'getIconPathAttribute')) {
                        $iconPath = $potion->getIconPathAttribute();
                    } else {
                        $iconPath = $potion->icon;

                        // Если путь начинается с /, то используем его относительно корня сайта
                        if (substr($iconPath, 0, 1) === '/') {
                            $iconPath = asset(substr($iconPath, 1));
                        }
                        // Если путь содержит 'icons/recipes/', заменяем на 'potions/'
                        elseif (strpos($iconPath, 'icons/recipes/') !== false) {
                            $iconName = basename($iconPath);
                            $iconPath = asset("assets/potions/{$iconName}");
                        }
                        // Если путь начинается с 'potions/', добавляем префикс assets/
                        elseif (strpos($iconPath, 'potions/') === 0) {
                            $iconPath = asset("assets/{$iconPath}");
                        }
                        // Если это просто имя файла без пути, предполагаем, что это в папке potions/
                        elseif (strpos($iconPath, '/') === false) {
                            $iconPath = asset("assets/potions/{$iconPath}");
                        }
                    }
                }

                // Позиция тултипа зависит от того, вертикально или горизонтально расположен бар
                $tooltipClass = $vertical
                    ? 'left-full top-1/2 -translate-y-1/2 translate-x-1 ml-1'
                    : '-mt-1 -translate-y-full top-0 left-1/2 transform -translate-x-1/2';
            ?>

            <div class="relative group">
                <div class="relative <?php echo e($potion ? 'animate-breathing-glow ' . $glowColorClass : ''); ?> rounded-md">
                    <div class="<?php echo e($slotClass); ?> <?php echo e($borderClass); ?> overflow-hidden shadow-md"
                        <?php if($potion): ?> data-potion-id="<?php echo e($potion->id); ?>" data-slot-position="<?php echo e($slotPosition); ?>" <?php endif; ?>>
                        <div class="absolute inset-0 bg-gradient-to-br from-[#3b3a33]/30 to-[#252117]/50 blur-[1px]">
                        </div>

                        <?php if(!$isEmpty): ?>
                            <img src="<?php echo e($iconPath); ?>" alt="<?php echo e($potion->name ?? 'Зелье'); ?>"
                                class="<?php echo e($iconSize); ?> object-contain quick-potion-icon"
                                style="image-rendering: crisp-edges;">

                            <div
                                class="absolute -bottom-[-1.5px] -right-[-0.5px] bg-[#252117] text-[#e5b769] rounded-full <?php echo e($useCountClass); ?> flex items-center justify-center border border-[#514b3c]">
                                <?php echo e(isset($potion->uses_left) && $potion->uses_left > 0 ? $potion->uses_left : ''); ?>

                            </div>

                            <?php if($showHotkeys): ?>
                                <div
                                    class="absolute <?php echo e($hotkeyClass); ?> px-0.5 bg-[#252117]/80 text-[#e5b769] rounded-sm ml-0.5 mb-0.5">
                                    <?php echo e($hotkeys[$slotPosition] ?? $slotPosition); ?>

                                </div>
                            <?php endif; ?>

                            <?php if(isset($potion->max_uses) && $potion->max_uses > 1): ?>
                                <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-[#38352c]">
                                    <div class="h-full bg-[#e5b769] bg-opacity-50"
                                        style="width: <?php echo e(($potion->uses_left / $potion->max_uses) * 100); ?>%"></div>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-[#514b3c] text-xs"><?php echo e($slotPosition); ?></div>

                            <?php if($showHotkeys): ?>
                                <div
                                    class="absolute <?php echo e($hotkeyClass); ?> px-0.5 bg-[#252117]/80 text-[#514b3c] rounded-sm ml-0.5 mb-0.5">
                                    <?php echo e($hotkeys[$slotPosition] ?? $slotPosition); ?>

                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if(!$isEmpty && $tooltips): ?>
                    <div
                        class="absolute z-50 hidden group-hover:block bg-[#252117] border border-[#514b3c] rounded-md p-1 text-xs shadow-lg <?php echo e($tooltipClass); ?> whitespace-nowrap">
                        <div class="text-[#e5b769] font-medium"><?php echo e($potion->name); ?></div>
                        <div class="text-gray-300 text-[10px]">
                            <?php echo e(isset($potion->uses_left) ? "Осталось: {$potion->uses_left}" : ''); ?></div>
                        <div class="text-[9px] text-gray-400 mt-0.5">Клавиша:
                            <?php echo e($hotkeys[$slotPosition] ?? $slotPosition); ?></div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Находим все слоты с зельями
        const potionSlots = document.querySelectorAll('[data-potion-id]');

        // Соответствие клавиш и слотов для горячих клавиш
        const defaultHotkeyMap = {
            '1': 1,
            '2': 2,
            '3': 3,
            '4': 4,
            '5': 5,
        };

        // Проверяем наличие пользовательских настроек в локальном хранилище
        let hotkeyMap = defaultHotkeyMap;
        const savedHotkeys = localStorage.getItem('potion_hotkeys');
        if (savedHotkeys) {
            try {
                const parsedHotkeys = JSON.parse(savedHotkeys);
                hotkeyMap = {
                    ...defaultHotkeyMap,
                    ...parsedHotkeys
                };
            } catch (e) {
                console.error('Ошибка при чтении сохраненных горячих клавиш:', e);
            }
        }

        // Добавляем обработчик для клавиатуры
        document.addEventListener('keydown', function(event) {
            // Проверяем, что не используется ввод в текстовое поле
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target
                .isContentEditable) {
                return;
            }

            // Проверяем, есть ли нажатая клавиша в нашей карте горячих клавиш
            const slotPosition = hotkeyMap[event.key];
            if (slotPosition) {
                // Находим слот с этой позицией
                const slot = document.querySelector(`[data-slot-position="${slotPosition}"]`);
                if (slot) {
                    // Имитируем клик по слоту
                    slot.click();

                    // Добавляем визуальный эффект нажатия
                    slot.classList.add('scale-90', 'opacity-70');
                    setTimeout(() => {
                        slot.classList.remove('scale-90', 'opacity-70');
                    }, 150);
                }
            }
        });

        // Добавляем обработчик клика для каждого слота
        potionSlots.forEach(slot => {
            slot.addEventListener('click', function() {
                const potionId = this.dataset.potionId;
                const slotPosition = this.dataset.slotPosition;

                // Добавляем эффект нажатия
                this.classList.add('scale-95', 'opacity-70');
                setTimeout(() => {
                    this.classList.remove('scale-95', 'opacity-70');
                }, 150);

                // Получаем CSRF-токен с проверкой на существование мета-тега
                let csrfToken = '';
                const csrfMeta = document.querySelector('meta[name="csrf-token"]');
                if (csrfMeta) {
                    csrfToken = csrfMeta.getAttribute('content');
                } else {
                    console.error(
                        'CSRF-токен не найден. Добавьте мета-тег csrf-token в head документа.'
                    );
                    showNotification('Ошибка безопасности: CSRF-токен не найден', 'error');
                    return;
                }

                // Отправляем запрос на использование зелья с пояса
                fetch('<?php echo e(route('inventory.potion-belt.use')); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        },
                        body: JSON.stringify({
                            slot_position: slotPosition
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Показываем уведомление об успешном использовании
                            showNotification(data.message, 'success');

                            // Если зелье было полностью использовано, обновляем слот
                            if (data.potion_removed) {
                                // Определяем классы в зависимости от компактности
                                const isEmpty = true;
                                const hotkeyClass = this.closest('#quick-potion-bar')
                                    .classList.contains('ultraCompact') ?
                                    'text-[8px] bottom-0 left-0' :
                                    'text-[9px] bottom-0 left-0';

                                // Получаем текущую клавишу для этого слота
                                const hotkey = Object.keys(hotkeyMap).find(key => hotkeyMap[
                                    key] == slotPosition) || slotPosition;

                                this.innerHTML = `
                                    <div class="absolute inset-0 bg-gradient-to-br from-[#3b3a33]/30 to-[#252117]/50 blur-[1px]"></div>
                                    <div class="text-[#514b3c] text-xs">${slotPosition}</div>
                                    <div class="absolute ${hotkeyClass} px-0.5 bg-[#252117]/80 text-[#514b3c] rounded-sm ml-0.5 mb-0.5">
                                        ${hotkey}
                                    </div>
                                `;
                                this.classList.remove('border-[#a6925e]');
                                this.classList.add('border-[#514b3c]');
                                this.removeAttribute('data-potion-id');

                                // Удаляем эффект свечения с родительского элемента
                                this.parentElement.classList.remove(
                                    'animate-breathing-glow');
                                if (this.parentElement.classList.length > 1) {
                                    this.parentElement.classList.remove(this.parentElement
                                        .classList[1]); // Удаляем класс цвета свечения
                                }
                            } else if (data.uses_left !== undefined) {
                                // Обновляем отображение количества использований, если зелье не было использовано полностью
                                const usesCountElement = this.querySelector(
                                    '[class*="rounded-full"]');
                                if (usesCountElement && data.uses_left > 0) {
                                    usesCountElement.textContent = data.uses_left;
                                }

                                // Обновляем полосу прогресса, если она есть
                                const progressBar = this.querySelector('.bg-[#e5b769]');
                                if (progressBar && data.max_uses) {
                                    progressBar.style.width =
                                        `${(data.uses_left / data.max_uses) * 100}%`;
                                }
                            }

                            // Обновляем HP/MP пользователя, если они изменились
                            if (data.current_hp !== undefined && data.max_hp !==
                                undefined) {
                                // Вычисляем количество восстановленного HP
                                const healAmount = data.heal_amount || 0;
                                updateHpBar(data.current_hp, data.max_hp, healAmount);
                            }

                            if (data.current_mp !== undefined && data.max_mp !==
                                undefined) {
                                updateMpBar(data.current_mp, data.max_mp);
                            }
                        } else {
                            // Показываем уведомление об ошибке
                            showNotification(data.message ||
                                'Произошла ошибка при использовании зелья', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Ошибка:', error);
                        showNotification('Произошла ошибка при использовании зелья',
                            'error');
                    });
            });

            // Добавляем обработчик контекстного меню для каждого слота (для настройки горячих клавиш)
            slot.addEventListener('contextmenu', function(e) {
                // Предотвращаем стандартное контекстное меню
                e.preventDefault();

                const slotPosition = this.dataset.slotPosition;

                // Запрашиваем у пользователя новую клавишу
                const currentKey = Object.keys(hotkeyMap).find(key => hotkeyMap[key] ==
                    slotPosition) || slotPosition;
                const newKey = prompt(
                    `Назначьте клавишу для слота ${slotPosition} (текущая: ${currentKey}):`,
                    currentKey);

                if (newKey && newKey.length > 0) {
                    // Удаляем старую привязку
                    Object.keys(hotkeyMap).forEach(key => {
                        if (hotkeyMap[key] == slotPosition) {
                            delete hotkeyMap[key];
                        }
                    });

                    // Добавляем новую привязку
                    hotkeyMap[newKey] = parseInt(slotPosition);

                    // Сохраняем настройки
                    localStorage.setItem('potion_hotkeys', JSON.stringify(hotkeyMap));

                    // Обновляем отображение клавиши
                    const hotkeyElem = this.querySelector('[class*="bg-[#252117]/80"]');
                    if (hotkeyElem) {
                        hotkeyElem.textContent = newKey;
                    }

                    showNotification(
                        `Клавиша ${newKey} теперь привязана к слоту ${slotPosition}`,
                        'success');
                }

                return false;
            });
        });

        // Функция для отображения уведомления
        function showNotification(message, type = 'success') {
            // Проверяем, существует ли уже контейнер для уведомлений
            let notificationContainer = document.getElementById('notification-container');

            if (!notificationContainer) {
                // Создаем контейнер, если его нет
                notificationContainer = document.createElement('div');
                notificationContainer.id = 'notification-container';
                // Изменяем позицию контейнера на нижнюю часть экрана
                notificationContainer.className =
                    'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col items-center space-y-2';
                document.body.appendChild(notificationContainer);
            }

            // Создаем элемент уведомления
            const notification = document.createElement('div');
            notification.className = `px-3 py-1.5 rounded-md shadow-lg transform transition-all duration-300 opacity-0 scale-95 ${
            type === 'success' ? 'bg-[#2c3e2e] text-[#a6e3b7] border border-[#5cb176]' :
            'bg-[#3e2c2c] text-[#e3b7a6] border border-[#b17651]'
        }`;
            notification.textContent = message;
            notification.style.maxWidth = '90%';
            notification.style.textAlign = 'center';
            notification.style.fontSize = '0.875rem';

            // Добавляем уведомление в контейнер
            notificationContainer.appendChild(notification);

            // Анимируем появление
            setTimeout(() => {
                notification.classList.remove('opacity-0', 'scale-95');
                notification.classList.add('opacity-100', 'scale-100');
            }, 10);

            // Удаляем уведомление через 2 секунды (уменьшаем время отображения)
            setTimeout(() => {
                notification.classList.remove('opacity-100', 'scale-100');
                notification.classList.add('opacity-0', 'scale-95');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 2000);
        }

        // Функция для обновления полосы HP
        function updateHpBar(currentHp, maxHp, healAmount = null) {
            const hpBars = document.querySelectorAll('.hp-bar');
            const hpTexts = document.querySelectorAll('.hp-text');

            if (hpBars.length > 0) {
                hpBars.forEach(bar => {
                    const percent = Math.min(100, Math.max(0, (currentHp / maxHp) * 100));
                    bar.style.width = `${percent}%`;
                });
            }

            if (hpTexts.length > 0) {
                hpTexts.forEach(text => {
                    text.textContent = `HP: ${currentHp}/${maxHp}`;
                });
            }

            // Если есть значение восстановления HP, показываем анимированный индикатор
            if (healAmount && healAmount > 0) {
                // Находим все контейнеры HP
                const hpContainers = document.querySelectorAll('.hp-container');

                hpContainers.forEach(container => {
                    // Создаем элемент для отображения восстановленного HP
                    const healIndicator = document.createElement('div');
                    healIndicator.className =
                        'absolute right-0 text-green-400 font-bold text-sm animate-fade-up';
                    healIndicator.textContent = `+${healAmount}`;
                    healIndicator.style.top = '-15px';

                    // Добавляем индикатор в контейнер HP
                    container.appendChild(healIndicator);

                    // Удаляем индикатор через 2 секунды
                    setTimeout(() => {
                        healIndicator.remove();
                    }, 2000);
                });
            }
        }

        // Функция для обновления полосы MP
        function updateMpBar(currentMp, maxMp) {
            const mpBars = document.querySelectorAll('.mp-bar');
            const mpTexts = document.querySelectorAll('.mp-text');

            if (mpBars.length > 0) {
                mpBars.forEach(bar => {
                    const percent = Math.min(100, Math.max(0, (currentMp / maxMp) * 100));
                    bar.style.width = `${percent}%`;
                });
            }

            if (mpTexts.length > 0) {
                mpTexts.forEach(text => {
                    text.textContent = `MP: ${currentMp}/${maxMp}`;
                });
            }
        }
    });
</script>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/user/quick-potion-bar.blade.php ENDPATH**/ ?>