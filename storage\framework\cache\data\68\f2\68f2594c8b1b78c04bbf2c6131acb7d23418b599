1753263897O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:14:"App\Models\Bot":30:{s:13:" * connection";s:5:"pgsql";s:8:" * table";s:4:"bots";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:132;s:4:"name";s:7:"бот1";s:2:"hp";i:155;s:6:"max_hp";i:155;s:4:"race";s:8:"lunarius";s:8:"location";s:24:"аааааааааааа";s:16:"mine_location_id";i:172;}s:11:" * original";a:7:{s:2:"id";i:132;s:4:"name";s:7:"бот1";s:2:"hp";i:155;s:6:"max_hp";i:155;s:4:"race";s:8:"lunarius";s:8:"location";s:24:"аааааааааааа";s:16:"mine_location_id";i:172;}s:10:" * changes";a:0:{}s:8:" * casts";a:13:{s:9:"is_active";s:7:"boolean";s:16:"last_attack_time";s:8:"datetime";s:12:"respawn_time";s:8:"datetime";s:18:"last_hp_regen_time";s:8:"datetime";s:18:"last_mp_regen_time";s:8:"datetime";s:16:"next_action_time";s:8:"datetime";s:20:"last_regeneration_at";s:8:"datetime";s:10:"death_time";s:8:"datetime";s:14:"can_use_skills";s:7:"boolean";s:21:"intelligent_targeting";s:7:"boolean";s:11:"can_retreat";s:7:"boolean";s:11:"is_peaceful";s:7:"boolean";s:16:"created_by_admin";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:36:{i:0;s:4:"name";i:1;s:4:"race";i:2;s:5:"class";i:3;s:5:"level";i:4;s:2:"hp";i:5;s:6:"max_hp";i:6;s:2:"mp";i:7;s:6:"max_mp";i:8;s:8:"strength";i:9;s:12:"intelligence";i:10;s:9:"dexterity";i:11;s:5:"armor";i:12;s:16:"magic_resistance";i:13;s:8:"location";i:14;s:16:"mine_location_id";i:15;s:9:"is_active";i:16;s:17:"current_target_id";i:17;s:19:"current_target_type";i:18;s:16:"last_attack_time";i:19;s:16:"last_attacker_id";i:20;s:18:"last_attacker_type";i:21;s:15:"attack_interval";i:22;s:22:"target_change_interval";i:23;s:18:"last_hp_regen_time";i:24;s:18:"last_mp_regen_time";i:25;s:14:"can_use_skills";i:26;s:21:"intelligent_targeting";i:27;s:11:"can_retreat";i:28;s:12:"respawn_time";i:29;s:16:"next_action_time";i:30;s:20:"last_regeneration_at";i:31;s:10:"death_time";i:32;s:16:"respawn_interval";i:33;s:11:"is_peaceful";i:34;s:16:"created_by_admin";i:35;s:11:"base_damage";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}