

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['type' => 'internal']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['type' => 'internal']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php if($type === 'home'): ?>
    
    <div class="mobtop-home-counter">
        
        <script type="text/javascript" src="//mobtop.com/c/134643.js"></script>
        <noscript>
            <a href="//mobtop.com/in/134643">
                <img src="//mobtop.com/134643.gif" alt="MobTop - Рейтинг и статистика мобильных сайтов" style="border: none;" />
            </a>
        </noscript>
    </div>
<?php else: ?>
    
    <div class="mobtop-internal-counter">
        
        <script type="text/javascript" src="//mobtop.com/c/134644.js"></script>
        <noscript>
            <a href="//mobtop.com/in/134644">
                <img src="//mobtop.com/134644.gif" alt="MobTop - Рейтинг и статистика мобильных сайтов" style="border: none;" />
            </a>
        </noscript>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/mobtop-counter.blade.php ENDPATH**/ ?>