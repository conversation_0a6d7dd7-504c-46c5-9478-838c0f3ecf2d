<?php $__env->startSection('title', 'Управление ресурсами локаций'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4">
    <h1 class="text-2xl font-bold text-[#e5b769] mb-6">Управление ресурсами локаций</h1>

    <?php if(session('success')): ?>
        <div class="bg-[#374529] border border-[#596c3b] text-[#b4d89b] px-4 py-3 rounded mb-4">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="bg-[#452929] border border-[#6c3b3b] text-[#d89b9b] px-4 py-3 rounded mb-4">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>

    <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg mb-6">
        <div class="px-6 py-4 border-b border-[#514b3c]">
            <h6 class="font-bold text-[#e5b769]">Фильтр ресурсов</h6>
        </div>
        <div class="p-6">
            <form action="<?php echo e(route('admin.location-resources.index')); ?>" method="GET">
                <div class="flex flex-col md:flex-row justify-between">
                    <div class="md:w-1/2 mb-4 md:mb-0">
                        <div class="mb-4">
                            <label for="location_selection" class="block text-[#e5b769] mb-1">Выберите локацию:</label>
                            <select name="location_selection" id="location_selection"
                                class="w-full bg-[#2a2721] border border-[#514b3c] rounded px-3 py-2 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e]"
                                onchange="this.form.submit()">
                                <?php $__currentLoopData = $hierarchicalLocations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $locationItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($locationItem['id']); ?>"
                                        <?php echo e($selectedLocationSelection == $locationItem['id'] ? 'selected' : ''); ?>

                                        <?php if($locationItem['type'] !== 'main'): ?>
                                            class="text-[#c1a96e] pl-4"
                                            style="padding-left: 1rem; color: #c1a96e;"
                                        <?php endif; ?>>
                                        <?php echo e($locationItem['display_name']); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <?php if(isset($locationData) && $locationData['display_name']): ?>
                            <div class="text-sm text-[#998d66] mb-2">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                Выбрано: <span class="text-[#c1a96e]"><?php echo e($locationData['display_name']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="flex items-center">
                        <a href="<?php echo e(route('admin.location-resources.create')); ?>" 
                           class="px-4 py-2 bg-[#5c4f2a] text-[#e5b769] rounded hover:bg-[#6c5f3a] transition duration-300 flex items-center">
                            <i class="fas fa-plus mr-2"></i> Добавить ресурс
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg mb-6">
        <div class="px-6 py-4 border-b border-[#514b3c]">
            <h6 class="font-bold text-[#e5b769]">Действия с ресурсами</h6>
        </div>
        <div class="p-6">
            <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 mb-4">
                <div class="md:w-1/2 pr-2">
                    <form action="<?php echo e(route('admin.location-resources.respawn-all')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="location_selection" value="<?php echo e($selectedLocationSelection); ?>">
                        <button type="submit"
                            class="px-4 py-2 bg-[#45592a] text-[#b4d89b] rounded hover:bg-[#556a3b] transition duration-300 flex items-center"
                            <?php if(!isset($locationData) || !$locationData['location_id']): ?> disabled <?php endif; ?>>
                            <i class="fas fa-sync mr-2"></i> Респаунить все ресурсы
                        </button>
                    </form>
                </div>
                <div class="md:w-1/2 pl-2">
                    <form action="<?php echo e(route('admin.location-resources.generate')); ?>" method="POST" class="flex flex-wrap items-center">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="location_selection" value="<?php echo e($selectedLocationSelection); ?>">
                        <div class="flex items-center mr-4">
                            <label for="count" class="text-[#d9d3b8] mr-2">Количество:</label>
                            <input type="number" name="count" id="count"
                                class="bg-[#2a2721] border border-[#514b3c] rounded px-3 py-1 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e] w-16"
                                value="5" min="1" max="20">
                        </div>
                        <button type="submit"
                            class="px-4 py-2 bg-[#5c4f2a] text-[#e5b769] rounded hover:bg-[#6c5f3a] transition duration-300 flex items-center"
                            <?php if(!isset($locationData) || !$locationData['location_id']): ?> disabled <?php endif; ?>>
                            <i class="fas fa-magic mr-2"></i> Сгенерировать ресурсы
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="flex justify-end">
                <form action="<?php echo e(route('admin.location-resources.delete-all')); ?>" method="POST" onsubmit="return confirm('Вы уверены, что хотите удалить ВСЕ ресурсы в этой локации? Это действие необратимо!')">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="location_selection" value="<?php echo e($selectedLocationSelection); ?>">
                    <button type="submit"
                        class="px-4 py-2 bg-[#592a2a] text-[#d89b9b] rounded hover:bg-[#6c3b3b] transition duration-300 flex items-center"
                        <?php if(!isset($locationData) || !$locationData['location_id']): ?> disabled <?php endif; ?>>
                        <i class="fas fa-trash mr-2"></i> Удалить все ресурсы
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg">
        <div class="px-6 py-4 border-b border-[#514b3c]">
            <h6 class="font-bold text-[#e5b769]">Список ресурсов</h6>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full bg-[#2a2721] border border-[#514b3c] rounded-lg overflow-hidden">
                    <thead>
                        <tr class="bg-[#3d3a2e] border-b border-[#514b3c]">
                            <th class="px-4 py-2 text-[#e5b769]">ID</th>
                            <th class="px-4 py-2 text-[#e5b769]">Ресурс</th>
                            <th class="px-4 py-2 text-[#e5b769]">Тип</th>
                            <th class="px-4 py-2 text-[#e5b769]">Локация</th>
                            <th class="px-4 py-2 text-[#e5b769]">Прочность</th>
                            <th class="px-4 py-2 text-[#e5b769]">Статус</th>
                            <th class="px-4 py-2 text-[#e5b769]">Респаун</th>
                            <th class="px-4 py-2 text-[#e5b769]">Действия</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $resources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="border-b border-[#514b3c] hover:bg-[#363229]">
                                <td class="px-4 py-2 text-[#d9d3b8]"><?php echo e($resource->id); ?></td>
                                <td class="px-4 py-2 text-[#d9d3b8]"><?php echo e($resource->resource->name ?? 'Неизвестно'); ?></td>
                                <td class="px-4 py-2 text-[#d9d3b8]"><?php echo e($resource->resource->type ?? 'Неизвестно'); ?></td>
                                <td class="px-4 py-2 text-[#d9d3b8]">
                                    <?php if($resource->mineLocation): ?>
                                        <span class="text-[#c1a96e]"><?php echo e($resource->location->name ?? 'Неизвестно'); ?> → <?php echo e($resource->mineLocation->name); ?></span>
                                    <?php else: ?>
                                        <span class="text-[#d9d3b8]"><?php echo e($resource->location->name ?? 'Неизвестно'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-2 text-[#d9d3b8]"><?php echo e($resource->durability); ?> / <?php echo e($resource->max_durability); ?></td>
                                <td class="px-4 py-2">
                                    <?php if($resource->is_active): ?>
                                        <span class="px-2 py-1 text-xs rounded-full bg-[#45592a] text-[#b4d89b]">Активен</span>
                                    <?php else: ?>
                                        <span class="px-2 py-1 text-xs rounded-full bg-[#592a2a] text-[#d89b9b]">Неактивен</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-2 text-[#d9d3b8]">
                                    <?php if($resource->respawn_at): ?>
                                        <?php echo e($resource->respawn_at->format('d.m.Y H:i')); ?>

                                    <?php else: ?>
                                        <span class="text-[#847f72]">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-2">
                                    <div class="flex space-x-2">
                                        <a href="<?php echo e(route('admin.location-resources.edit', $resource->id)); ?>" 
                                           class="px-2 py-1 bg-[#5c4f2a] text-[#e5b769] rounded hover:bg-[#6c5f3a] transition duration-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.location-resources.destroy', $resource->id)); ?>" method="POST" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                class="px-2 py-1 bg-[#592a2a] text-[#d89b9b] rounded hover:bg-[#6c3b3b] transition duration-300" 
                                                onclick="return confirm('Вы уверены, что хотите удалить этот ресурс?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="px-4 py-4 text-center text-[#847f72]">Ресурсы не найдены</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <div class="mt-6">
                <?php echo e($resources->appends(['location_selection' => $selectedLocationSelection])->links()); ?>

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/location-resources/index.blade.php ENDPATH**/ ?>