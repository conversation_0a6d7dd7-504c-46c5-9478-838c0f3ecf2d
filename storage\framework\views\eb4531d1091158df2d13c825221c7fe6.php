<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['battleLogs' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['battleLogs' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="mt-4 w-full">
    <div class="w-full bg-[#232218] border border-[#a6925e] rounded-md shadow-lg overflow-hidden max-w-md mx-auto">
        
        <div
            class="flex items-center justify-center bg-gradient-to-r from-[#3d3928] to-[#4a4532] border-b border-[#a6925e] px-1 py-2 relative">
            <div class="absolute left-0 top-0 bottom-0 w-6 flex items-center justify-center">
                <svg class="w-4 h-4 text-[#c9aa6e]" fill="currentColor" viewBox="0 0 20 20">
                    <path
                        d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z">
                    </path>
                </svg>
            </div>
            <h3 class="text-[#ffd046] text-sm font-bold tracking-wide text-center px-6">Журнал боя</h3>
            <div class="absolute right-0 top-0 bottom-0 w-6 flex items-center justify-center">
                <svg class="w-4 h-4 text-[#c9aa6e]" fill="currentColor" viewBox="0 0 20 20">
                    <path
                        d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                    </path>
                </svg>
            </div>
        </div>

        
        <div id="battleLogsContainer" class="max-h-44 overflow-y-auto custom-scrollbar"
            style="scrollbar-width: thin; scrollbar-color: #a6925e #232218;">
            <?php $__empty_1 = true; $__currentLoopData = $battleLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="px-3 py-1.5 border-b border-[#3b3a2a] transition-colors duration-150 hover:bg-[#2a2920]">
                    <div class="flex items-center justify-between">
                        
                        <span class="inline-block w-7 text-xs text-[#a6925e] mr-2 font-mono whitespace-nowrap text-left">
                            <?php echo e(\Carbon\Carbon::parse($log['timestamp'] ?? now())->format('H:i')); ?>

                        </span>

                        
                        <div class="flex-1 min-w-0">
                            <?php if(str_contains($log['message'], '<img')): ?>
                                <div><?php echo $log['message']; ?></div>
                            <?php else: ?>
                                            <p class="text-xs leading-tight break-words
                                                            <?php echo e($log['type'] === 'success'
                                ? 'text-[#4ADE80]'
                                : ($log['type'] === 'danger'
                                    ? 'text-[#FF6B4A]'
                                    : ($log['type'] === 'warning'
                                        ? 'text-[#FFD046]'
                                        : 'text-[#e0e0d0]'))); ?>">
                                                <?php echo $log['message']; ?>

                                            </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="flex flex-col items-center justify-center py-6 px-2 text-center">
                    <svg class="w-5 h-5 mb-1 text-[#8b8b70]" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <p class="text-xs text-[#a09a80]">Журнал пуст</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/battle-logs.blade.php ENDPATH**/ ?>