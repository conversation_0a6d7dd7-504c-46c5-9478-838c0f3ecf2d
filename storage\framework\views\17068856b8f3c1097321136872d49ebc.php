<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['isDisabled' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['isDisabled' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="container mx-auto text-center px-2 py-1 flex justify-center space-x-2">
    <?php if($isDisabled): ?>
        
        <span class="bg-[#6b6658] text-[#a8a090] py-1 px-1 rounded shadow-inner cursor-not-allowed opacity-70">
            Рюкзак
        </span>
        <span class="bg-[#6b6658] text-[#a8a090] py-1 px-1 rounded shadow-inner cursor-not-allowed opacity-70">
            Персонаж
        </span>
        <span class="bg-[#6b6658] text-[#a8a090] py-1 px-1 rounded shadow-inner cursor-not-allowed opacity-70">
            Группа
        </span>
        <span class="bg-[#6b6658] text-[#a8a090] py-1 px-1 rounded shadow-inner cursor-not-allowed opacity-70">
            Гильдия
        </span>
    <?php else: ?>
        
        <a href="<?php echo e(route('inventory.index')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Рюкзак
        </a>
        <a href="<?php echo e(route('user.profile')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Персонаж
        </a>
        <a href="<?php echo e(route('party.index')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Группа
        </a>
        <a href="<?php echo e(route('guilds.index')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Гильдия
        </a>
    <?php endif; ?>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/navigation-buttons.blade.php ENDPATH**/ ?>