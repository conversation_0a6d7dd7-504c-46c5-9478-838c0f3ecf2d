<?php
    // Определяем классы для разных типов сообщений в игровом стиле
    $messageTypes = [
        'success' => [
            'bg' => 'bg-[#36513f]',
            'text' => 'text-[#c8ffdb]',
            'border' => 'border-[#4a7759]',
            'icon' => '✅',
        ],
        'error' => [
            'bg' => 'bg-[#613f36]',
            'text' => 'text-[#ffeac1]',
            'border' => 'border-[#88634a]',
            'icon' => '❌',
        ],
        'warning' => [
            'bg' => 'bg-[#5e553a]',
            'text' => 'text-[#ffe7bd]',
            'border' => 'border-[#7d7248]',
            'icon' => '⚠️',
        ],
        'info' => [
            'bg' => 'bg-[#3a4a5e]',
            'text' => 'text-[#c1dcff]',
            'border' => 'border-[#4a617d]',
            'icon' => 'ℹ️',
        ],
        'party_action' => [
            'bg' => 'bg-[#613f36]',
            'text' => 'text-[#ffcbb0]',
            'border' => 'border-[#88634a]',
            'icon' => '👥',
        ],
    ];

    // Redis флеш-сообщения теперь отображаются в компоненте location-name
?>

<div class="game-flash-messages">
    
    <?php if(session('success')): ?>
        <div
            class="<?php echo e($messageTypes['success']['bg']); ?> <?php echo e($messageTypes['success']['text']); ?> p-1.5 mx-2 text-xs rounded border <?php echo e($messageTypes['success']['border']); ?> shadow-inner shadow-black/30 animate-fade-in">
            <?php echo session('success'); ?>

        </div>
    <?php endif; ?>

    
    <?php if(session('error')): ?>
        <div
            class="<?php echo e($messageTypes['error']['bg']); ?> <?php echo e($messageTypes['error']['text']); ?> p-1.5 mx-2 text-xs rounded border <?php echo e($messageTypes['error']['border']); ?> shadow-inner shadow-black/30 animate-fade-in">
            <?php echo session('error'); ?>

        </div>
    <?php endif; ?>

    
    <?php if(session('warning')): ?>
        <div
            class="<?php echo e($messageTypes['warning']['bg']); ?> <?php echo e($messageTypes['warning']['text']); ?> p-1.5 mx-2 text-xs rounded border <?php echo e($messageTypes['warning']['border']); ?> shadow-inner shadow-black/30 animate-fade-in">
            <?php echo session('warning'); ?>

        </div>
    <?php endif; ?>

    
    <?php if(session('info')): ?>
        <div
            class="<?php echo e($messageTypes['info']['bg']); ?> <?php echo e($messageTypes['info']['text']); ?> p-1.5 mx-2 text-xs rounded border <?php echo e($messageTypes['info']['border']); ?> shadow-inner shadow-black/30 animate-fade-in">
            <?php echo session('info'); ?>

        </div>
    <?php endif; ?>

    
    <?php if(session('party_action')): ?>
        <div
            class="<?php echo e($messageTypes['party_action']['bg']); ?> <?php echo e($messageTypes['party_action']['text']); ?> p-1.5 mx-2 text-xs rounded border <?php echo e($messageTypes['party_action']['border']); ?> shadow-inner shadow-black/30 animate-fade-in">
            <?php echo session('party_action'); ?>

        </div>
    <?php endif; ?>

    

    
    <?php if(session('welcome_message')): ?>
        <div
            class="bg-[#3b3a33] text-[#e5b769] p-2 rounded mb-2 mt-2 border border-[#a6925e] shadow-md animate-fade-in text-center">
            <?php echo e(session('welcome_message')); ?>

        </div>
    <?php endif; ?>

    
    <?php if(session('dungeon_access_blocked')): ?>
        <?php
            $blockData = session('dungeon_access_blocked');
        ?>
        <div
            class="bg-gradient-to-r from-[#6c4539] to-[#2a1b12] border border-[#6c4539] rounded-lg p-3 mx-2 my-2 animate-fade-in">
            <div class="flex items-center space-x-2 mb-2">
                <span class="text-[#f28b38] text-lg">⚠️</span>
                <span class="text-[#fceac4] font-semibold">Вы находитесь в подземелье</span>
            </div>
            <p class="text-[#fceac4] text-sm leading-relaxed mb-3">
                <?php echo e($blockData['message']); ?>

            </p>
            <p class="text-[#a6925e] text-xs mb-3">
                Если вы покидаете подземелье, то также покидаете группу.
            </p>

            
            <div class="flex justify-center">
                
                <button onclick="this.parentElement.parentElement.style.display='none'"
                    class="bg-gradient-to-br from-[#3e5c48] to-[#243c2f] text-[#fceac4] font-semibold py-2 px-4 rounded border border-[#3e5c48] hover:from-[#4a6b54] hover:to-[#2d4738] transition-all duration-300 text-sm">
                    ❌ Закрыть уведомление
                </button>
            </div>
        </div>
    <?php endif; ?>

    
</div>


<style>
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeIn 0.3s ease-out forwards;
    }
</style><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/game-flash-messages.blade.php ENDPATH**/ ?>