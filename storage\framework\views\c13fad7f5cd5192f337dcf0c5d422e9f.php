<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['mobsInLocation' => [], 'user' => null, 'routePrefix' => 'battle.mines.custom']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['mobsInLocation' => [], 'user' => null, 'routePrefix' => 'battle.mines.custom']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="mt-2 m-0 p-0 relative z-10">
    <div class="grid grid-cols-3 gap-2 justify-items-center">
        <?php $__empty_1 = true; $__currentLoopData = $mobsInLocation; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mob): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php
                $isSelected = $user->current_target_id == $mob->id && $user->current_target_type == 'mob';
                $hpPercent = (($mob->current_hp ?? $mob->hp) / ($mob->max_hp ?? $mob->hp)) * 100;
                
                // Динамическое изменение цвета от зеленого к красному
                if ($hpPercent > 80) {
                    $hpColor = '#22C55E'; // Зеленый
                    $hpGlow = 'rgba(34, 197, 94, 0.6)';
                } elseif ($hpPercent > 60) {
                    $hpColor = '#84CC16'; // Лайм-зеленый
                    $hpGlow = 'rgba(132, 204, 22, 0.6)';
                } elseif ($hpPercent > 40) {
                    $hpColor = '#EAB308'; // Желтый
                    $hpGlow = 'rgba(234, 179, 8, 0.6)';
                } elseif ($hpPercent > 25) {
                    $hpColor = '#F97316'; // Оранжевый
                    $hpGlow = 'rgba(249, 115, 22, 0.7)';
                } elseif ($hpPercent > 10) {
                    $hpColor = '#EF4444'; // Красный
                    $hpGlow = 'rgba(239, 68, 68, 0.8)';
                } else {
                    $hpColor = '#DC2626'; // Темно-красный
                    $hpGlow = 'rgba(220, 38, 38, 0.9)';
                }
            ?>
            <form action="<?php echo e(route($routePrefix . '.select-mob', [request()->route('slug'), $mob->id])); ?>" method="POST"
                class="m-0 p-0" style="margin: 0; padding: 0;">
                <?php echo csrf_field(); ?>
                <button type="submit" 
                    class="w-20 h-24 m-0 p-1 relative transform transition-all duration-200 hover:scale-105 <?php echo e($isSelected ? 'mob-card-selected' : 'mob-card-default'); ?>"
                    style="margin: 0; padding: 2px;">
                    
                    
                    <div class="absolute inset-0 rounded-lg bg-gradient-to-b from-[#3b3629] to-[#2a2722] border <?php echo e($isSelected ? 'border-[#c1a96e] shadow-glow' : 'border-[#6e3f35]/60'); ?>"></div>
                    
                    <div class="relative z-10 flex flex-col items-center h-full justify-between py-1">
                        
                        <div class="relative mb-1">
                            <img src="<?php echo e(asset($mob->icon ?? 'assets/wolfIcon.png')); ?>" alt="<?php echo e($mob->name); ?>"
                                class="w-12 h-12 rounded-full <?php echo e($isSelected ? 'shadow-icon-glow' : ''); ?> border border-[#3b3629]">

                            
                            <div class="absolute -bottom-1 -right-1 bg-gradient-to-b from-[#6e3f35] to-[#59372d] text-[#f8eac2] text-[8px] rounded-full w-3.5 h-3.5 flex items-center justify-center border border-[#c1a96e] shadow-sm">
                                <?php echo e($mob->level ?? '?'); ?>

                            </div>
                        </div>

                        
                        <div class="w-full mb-1">
                            <?php if (isset($component)) { $__componentOriginal6a1b6be21287526a0edc0a7cfd4462cd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6a1b6be21287526a0edc0a7cfd4462cd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mob-active-effects','data' => ['mob' => $mob,'maxEffects' => 3]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mob-active-effects'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['mob' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($mob),'maxEffects' => 3]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6a1b6be21287526a0edc0a7cfd4462cd)): ?>
<?php $attributes = $__attributesOriginal6a1b6be21287526a0edc0a7cfd4462cd; ?>
<?php unset($__attributesOriginal6a1b6be21287526a0edc0a7cfd4462cd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6a1b6be21287526a0edc0a7cfd4462cd)): ?>
<?php $component = $__componentOriginal6a1b6be21287526a0edc0a7cfd4462cd; ?>
<?php unset($__componentOriginal6a1b6be21287526a0edc0a7cfd4462cd); ?>
<?php endif; ?>
                        </div>

                        
                        <div class="w-full h-3 bg-[#1a1814] rounded-sm relative border border-[#3b3629] overflow-hidden">
                            
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#2a2722]/20 to-transparent"></div>
                            
                            
                            <div class="h-full rounded-sm transition-all duration-300 relative"
                                style="width: <?php echo e($hpPercent); ?>%; background: linear-gradient(to right, <?php echo e($hpColor); ?>, <?php echo e($hpColor); ?>CC); box-shadow: 0 0 4px <?php echo e($hpGlow); ?>;">
                                
                                <div class="absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-transparent"></div>
                            </div>
                            
                            
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-[#f8eac2] text-[9px] font-bold drop-shadow-[0_0_2px_rgba(0,0,0,1)]"><?php echo e($mob->current_hp ?? $mob->hp); ?></span>
                            </div>
                        </div>
                    </div>
                </button>
            </form>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-3 text-center py-4">
                <div class="bg-gradient-to-b from-[#3b3629] to-[#2a2722] border border-[#6e3f35]/60 rounded-lg p-3">
                    <p class="text-[#998d66] text-sm">Монстры отсутствуют в этой локации</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    /* Анимация золотого свечения для выбранного моба */
    @keyframes goldenGlow {
        0% {
            box-shadow: 0 0 8px rgba(193, 169, 110, 0.4), inset 0 0 8px rgba(193, 169, 110, 0.1);
        }
        50% {
            box-shadow: 0 0 16px rgba(193, 169, 110, 0.7), inset 0 0 12px rgba(193, 169, 110, 0.2);
        }
        100% {
            box-shadow: 0 0 8px rgba(193, 169, 110, 0.4), inset 0 0 8px rgba(193, 169, 110, 0.1);
        }
    }

    /* Анимация для иконки выбранного моба */
    @keyframes iconMysticGlow {
        0% {
            box-shadow: 0 0 4px rgba(193, 169, 110, 0.5);
        }
        50% {
            box-shadow: 0 0 8px rgba(193, 169, 110, 0.8), 0 0 12px rgba(193, 169, 110, 0.3);
        }
        100% {
            box-shadow: 0 0 4px rgba(193, 169, 110, 0.5);
        }
    }

    /* Базовые стили для карточек мобов */
    .mob-card-default {
        transition: all 0.3s ease;
    }

    .mob-card-default:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .mob-card-selected {
        animation: goldenGlow 2.5s infinite ease-in-out;
    }

    .shadow-glow {
        box-shadow: 0 0 12px rgba(193, 169, 110, 0.6);
    }

    .shadow-icon-glow {
        animation: iconMysticGlow 2.5s infinite ease-in-out;
    }

    /* Улучшенные тени для текста */
    .text-shadow-strong {
        text-shadow: 0 0 3px rgba(0, 0, 0, 0.8), 0 0 6px rgba(0, 0, 0, 0.6);
    }
</style><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mines/mob-list.blade.php ENDPATH**/ ?>